<template>
  <LoginComponent :isLocal="isLocal" :options="options" />
</template>

<script setup>
import { ref, computed } from 'vue'
import { LoginComponent } from 'commonkit-login-vue3'
import { loginType } from '@utils/storage.js'

const isLocal = ref(import.meta.env.VITE_ENV === 'local')

const options = computed(() => ({
  useHistoryReplace: true,
  loginType: loginType.get() || '0'
}))
</script>
