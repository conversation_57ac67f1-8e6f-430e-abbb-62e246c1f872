<template>
  <MainLayout scroll="auto">
    <div class="page-category">
      <!-- 顶部搜索区域 -->
      <SearchHeader v-model="searchKeyword" placeholder="搜索商品" :redirectToSearch="true" redirectUrl="/search"
        @search="handleSearch" />

      <!-- 分类内容区域 -->
      <div class="category-layout">
        <!-- 左侧一级分类导航 -->
        <aside class="category-sidebar">
          <!-- 一级分类骨架屏加载状态 -->
          <div v-if="isFirstCategoryLoading" class="sidebar-skeleton">
            <div v-for="i in 8" :key="i" class="skeleton-sidebar-item">
              <div class="skeleton-sidebar-text"></div>
            </div>
          </div>

          <!-- 一级分类导航内容 -->
          <van-sidebar v-else v-model="activeFirstCategory" @change="handleFirstCategoryChange" ref="sidebar">
            <van-sidebar-item v-for="category in firstCategories" :key="category.id" :title="category.name"
              ref="sidebarItems" />
          </van-sidebar>
        </aside>

        <!-- 右侧分类内容区域 -->
        <main class="category-main" ref="categoryContainer">
          <!-- 一级分类加载时的骨架屏 -->
          <div v-if="isFirstCategoryLoading" class="skeleton-wrapper">
            <div v-for="i in 3" :key="i" class="skeleton-section">
              <div class="skeleton-section-title"></div>
              <div class="skeleton-category-grid">
                <div v-for="j in 3" :key="j" class="skeleton-category-item">
                  <div class="skeleton-item-icon"></div>
                  <div class="skeleton-item-name"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 二级分类加载时的骨架屏 -->
          <div v-else-if="isSecondCategoryLoading" class="skeleton-wrapper">
            <div v-for="i in 3" :key="i" class="skeleton-section">
              <div class="skeleton-section-title"></div>
              <div class="skeleton-category-grid">
                <div v-for="j in 3" :key="j" class="skeleton-category-item">
                  <div class="skeleton-item-icon"></div>
                  <div class="skeleton-item-name"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 分类内容 -->
          <div v-else class="category-sections">
            <section v-for="(group, index) in thirdCategoriesGroups" :key="group.id || index" class="category-section"
              :data-category-id="group.id">
              <h3 class="section-title">{{ group.title }}</h3>

              <!-- 三级分类项目 -->
              <div v-if="group.items.length > 0" class="category-items">
                <div v-for="item in group.items" :key="item.id" class="category-item" :style="{ width: itemWidthStyle }"
                  @click="handleCategoryClick(item)">
                  <div class="item-icon">
                    <img :src="item.img || defaultIcon" :alt="item.name" loading="lazy" decoding="async" width="60"
                      height="60" />
                  </div>
                  <span class="item-name">{{ item.name }}</span>
                </div>
              </div>

              <!-- 加载状态 - 使用统一的骨架屏 -->
              <div v-else-if="group.isLoading" class="skeleton-category-grid">
                <div v-for="j in 3" :key="j" class="skeleton-category-item">
                  <div class="skeleton-item-icon"></div>
                  <div class="skeleton-item-name"></div>
                </div>
              </div>

              <!-- 空状态 -->
              <div v-else-if="group.isEmpty" class="empty-placeholder">
                <span class="empty-text">暂无商品分类</span>
              </div>

              <!-- 未加载状态（占位） -->
              <div v-else class="placeholder-section">
                <div class="load-trigger" :data-second-id="group.id"></div>
              </div>
            </section>
          </div>
        </main>
      </div>
    </div>
  </MainLayout>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, onBeforeUnmount, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { closeToast, showLoadingToast, showToast } from 'vant'
import SearchHeader from "@components/Common/SearchHeader.vue"
import { getClassification } from '@api/interface/goods.js'
import { getBizCode } from "@utils/curEnv.js"
import MainLayout from '@components/Common/MainLayout/MainLayout.vue'


// 路由实例
const router = useRouter()
const route = useRoute()

// 搜索关键词
const searchKeyword = ref('')

// 默认图标
const defaultIcon = 'https://img01.yzcdn.cn/vant/cat.jpeg'

// 分类数据
const firstCategories = ref([])
const secondCategories = ref([])
const thirdCategories = ref(new Map()) // 改为Map结构，key为二级分类ID

// 当前选中的一级分类索引
const activeFirstCategory = ref(0)

// 加载状态优化 - 分别控制一级、二级、三级分类的加载状态
const isFirstCategoryLoading = ref(true) // 一级分类加载状态
const isSecondCategoryLoading = ref(false) // 二级分类加载状态
const loadingThirdCategories = ref(new Set()) // 正在加载的三级分类ID集合

// 兼容性保持
const isInitialLoading = computed(() => isFirstCategoryLoading.value) // 初始加载状态，用于显示骨架屏
const loading = ref(false)

// API缓存优化
const apiCache = new Map()
const getCacheKey = (params) => JSON.stringify(params)

// 懒加载相关
const INITIAL_LOAD_COUNT = 3 // 初始加载的二级分类数量
const LOAD_THRESHOLD = 200 // 距离底部多少像素时开始加载
const loadedSecondCategoryIds = ref(new Set()) // 已加载三级分类的二级分类ID集合

// 分类容器引用
const categoryContainer = ref(null)

// 侧边栏引用
const sidebar = ref(null)
const sidebarItems = ref([])

// 每个分类项的最小宽度（像素）
const MIN_ITEM_WIDTH = 100
// 每行最少显示的项目数
const MIN_ITEMS_PER_ROW = 3

// 计算每个分类项的宽度 - 缓存计算结果
const itemsPerRow = ref(MIN_ITEMS_PER_ROW)
const itemWidthStyle = computed(() => `${100 / itemsPerRow.value}%`)

// 防抖优化 - 计算每行应该显示的项目数量
let resizeTimer = null
const calculateItemsPerRow = () => {
  if (!categoryContainer.value) return

  // 使用requestAnimationFrame优化性能
  requestAnimationFrame(() => {
    const containerWidth = categoryContainer.value.clientWidth
    const maxItemsPerRow = Math.floor(containerWidth / MIN_ITEM_WIDTH)
    itemsPerRow.value = Math.max(maxItemsPerRow, MIN_ITEMS_PER_ROW)
  })
}

// 将三级分类分组显示 - 支持懒加载
const thirdCategoriesGroups = computed(() => {
  const groups = []

  // 按照二级分类分组
  secondCategories.value.forEach(secondCategory => {
    const items = thirdCategories.value.get(secondCategory.id) || []
    const isLoading = loadingThirdCategories.value.has(secondCategory.id)
    const isLoaded = loadedSecondCategoryIds.value.has(secondCategory.id)

    groups.push({
      id: secondCategory.id,
      title: secondCategory.name,
      items: items,
      isLoading: isLoading,
      isLoaded: isLoaded,
      isEmpty: isLoaded && items.length === 0
    })
  })

  return groups
})

// 搜索处理函数
const handleSearch = () => {
  // 搜索功能由 SearchHeader 组件的 redirectToSearch 处理
}


// 根据分类ID查找并设置当前选中的一级分类
const findAndSetActiveCategoryById = (categoryId) => {
  if (!categoryId || !firstCategories.value.length) return false

  const index = firstCategories.value.findIndex(category => category.id === categoryId)
  if (index !== -1) {
    activeFirstCategory.value = index
    handleFirstCategoryChange(index)

    // 添加滚动到对应一级分类的功能
    nextTick(() => {
      if (sidebar.value) {
        // 获取侧边栏元素
        const sidebarEl = sidebar.value.$el
        // 获取选中的项目元素
        const selectedItem = sidebarItems.value[index]?.$el

        if (sidebarEl && selectedItem) {
          // 计算需要滚动的位置
          const itemTop = selectedItem.offsetTop
          const sidebarHeight = sidebarEl.clientHeight
          const itemHeight = selectedItem.clientHeight

          // 滚动到使当前项居中的位置
          sidebarEl.scrollTo({
            top: itemTop - (sidebarHeight / 2) + (itemHeight / 2),
            behavior: 'smooth'
          })
        }
      }
    })

    return true
  }
  return false
}

// 获取分类数据 - 优化版本
const fetchCategories = async (id = '') => {
  const cacheKey = getCacheKey({ category_pid: id, page_no: 1, page_size: 500 })

  try {
    // 根据不同级别设置不同的加载状态
    if (id === '') {
      // 获取一级分类
      isFirstCategoryLoading.value = true
    } else {
      // 获取二级分类时，如果不是初始加载，显示二级分类加载状态
      if (!isFirstCategoryLoading.value) {
        isSecondCategoryLoading.value = true
        showLoadingToast()
      }
    }
    loading.value = true

    // 检查缓存
    if (apiCache.has(cacheKey)) {
      const cachedData = apiCache.get(cacheKey)
      processCategories(cachedData, id)
      return
    }

    // 调用API获取分类数据
    const [err, data] = await getClassification({
      bizCode: getBizCode('GOODS'),
      category_pid: id,
      page_no: 1,
      page_size: 500
    })

    if (err) {
      showToast('获取分类数据失败')
      return
    }

    if (data && Array.isArray(data)) {
      // 缓存数据（5分钟过期）
      apiCache.set(cacheKey, data)
      setTimeout(() => apiCache.delete(cacheKey), 5 * 60 * 1000)

      processCategories(data, id)
    }
  } catch (error) {
    console.error('获取分类数据出错:', error)
    showToast('获取分类数据失败')
  } finally {
    loading.value = false
    // 根据不同级别关闭对应的加载状态
    if (id === '') {
      isFirstCategoryLoading.value = false
    } else {
      isSecondCategoryLoading.value = false
    }
    closeToast()
  }
}

// 处理分类数据 - 支持懒加载
const processCategories = async (data, id) => {
  if (id === '') {
    // 获取一级分类
    firstCategories.value = data.filter(item => item.depth === 1)

    // 获取路由参数中的分类ID
    const routeCategoryId = route.params.id

    // 如果有路由参数ID，则尝试查找并设置对应的一级分类
    if (routeCategoryId && firstCategories.value.length > 0) {
      const found = findAndSetActiveCategoryById(routeCategoryId)
      if (!found && firstCategories.value.length > 0) {
        activeFirstCategory.value = 0
        await fetchSecondAndThirdCategories(firstCategories.value[0].id)
      }
    } else if (firstCategories.value.length > 0) {
      activeFirstCategory.value = 0
      await fetchSecondAndThirdCategories(firstCategories.value[0].id)
    }
  } else {
    // 获取二级分类
    const secondLevel = data.filter(item => item.depth === 2)
    secondCategories.value = secondLevel

    // 清空之前的三级分类数据和加载状态
    thirdCategories.value.clear()
    loadedSecondCategoryIds.value.clear()
    loadingThirdCategories.value.clear()

    // 只加载前几个二级分类的三级分类
    if (secondLevel.length > 0) {
      await loadInitialThirdCategories(secondLevel)

      // 设置滚动监听，实现懒加载
      nextTick(() => {
        setupScrollListener()
      })
    }
  }
}

// 获取二级和三级分类
const fetchSecondAndThirdCategories = async (firstCategoryId) => {
  if (!firstCategoryId) return

  loading.value = true
  await fetchCategories(firstCategoryId)
}

// 加载初始的几个二级分类的三级分类
const loadInitialThirdCategories = async (secondLevel) => {
  const initialCategories = secondLevel.slice(0, INITIAL_LOAD_COUNT)

  // 并行加载前几个二级分类的三级分类
  const loadPromises = initialCategories.map(async (secondCategory) => {
    loadingThirdCategories.value.add(secondCategory.id)

    try {
      const thirdItems = await fetchThirdCategories(secondCategory.id)
      thirdCategories.value.set(secondCategory.id, thirdItems)
      loadedSecondCategoryIds.value.add(secondCategory.id)
    } catch (error) {
      console.error(`加载二级分类 ${secondCategory.id} 的三级分类失败:`, error)
      thirdCategories.value.set(secondCategory.id, [])
      loadedSecondCategoryIds.value.add(secondCategory.id)
    } finally {
      loadingThirdCategories.value.delete(secondCategory.id)
    }
  })

  await Promise.all(loadPromises)
}

// 获取单个二级分类的三级分类
const fetchThirdCategories = async (secondCategoryId) => {
  if (!secondCategoryId) return []

  const cacheKey = getCacheKey({ category_pid: secondCategoryId, page_no: 1, page_size: 500 })

  // 检查缓存
  if (apiCache.has(cacheKey)) {
    const cachedData = apiCache.get(cacheKey)
    return cachedData.filter(item => item.depth === 3)
  }

  try {
    const [err, data] = await getClassification({
      bizCode: getBizCode('GOODS'),
      category_pid: secondCategoryId,
      page_no: 1,
      page_size: 500
    })

    if (err || !data) return []

    // 缓存数据
    apiCache.set(cacheKey, data)
    setTimeout(() => apiCache.delete(cacheKey), 5 * 60 * 1000)

    return data.filter(item => item.depth === 3)
  } catch (error) {
    console.error('获取三级分类出错:', error)
    return []
  }
}

// 懒加载单个二级分类的三级分类
const loadThirdCategoryLazy = async (secondCategoryId) => {
  if (loadedSecondCategoryIds.value.has(secondCategoryId) ||
    loadingThirdCategories.value.has(secondCategoryId)) {
    return
  }

  loadingThirdCategories.value.add(secondCategoryId)

  try {
    const thirdItems = await fetchThirdCategories(secondCategoryId)
    thirdCategories.value.set(secondCategoryId, thirdItems)
    loadedSecondCategoryIds.value.add(secondCategoryId)
  } catch (error) {
    console.error(`懒加载二级分类 ${secondCategoryId} 的三级分类失败:`, error)
    thirdCategories.value.set(secondCategoryId, [])
    loadedSecondCategoryIds.value.add(secondCategoryId)
  } finally {
    loadingThirdCategories.value.delete(secondCategoryId)
  }
}

// 一级分类切换处理函数
const handleFirstCategoryChange = (index) => {
  const selectedCategory = firstCategories.value[index]
  if (!selectedCategory) return

  // 获取选中一级分类的ID
  const firstCategoryId = selectedCategory.id

  // 立即重置滚动位置到顶部（无动画，无感知）
  if (categoryContainer.value) {
    categoryContainer.value.scrollTop = 0
  }

  // 更新路由参数，但不重新加载页面
  router.replace({ path: `/category/${firstCategoryId}` }, () => { }, { shallow: true })

  // 调用API获取对应的二级和三级分类
  fetchSecondAndThirdCategories(firstCategoryId)
}

// 分类项点击处理函数
const handleCategoryClick = (category) => {
  // 跳转到对应分类的商品列表页
  router.push({
    path: `/goodslist/${category.id}`,
  })
}

// 滚动监听器引用
let scrollListener = null

// 设置滚动监听，实现懒加载
const setupScrollListener = () => {
  if (!categoryContainer.value) return

  // 移除之前的监听器
  if (scrollListener) {
    categoryContainer.value.removeEventListener('scroll', scrollListener)
  }

  // 防抖处理滚动事件
  let scrollTimer = null
  scrollListener = () => {
    if (scrollTimer) {
      clearTimeout(scrollTimer)
    }

    scrollTimer = setTimeout(() => {
      checkAndLoadVisibleCategories()
    }, 100)
  }

  categoryContainer.value.addEventListener('scroll', scrollListener, { passive: true })

  // 初始检查
  nextTick(() => {
    checkAndLoadVisibleCategories()
  })
}

// 检查并加载可见的分类
const checkAndLoadVisibleCategories = () => {
  if (!categoryContainer.value) return

  const container = categoryContainer.value
  const containerRect = container.getBoundingClientRect()
  const containerTop = containerRect.top
  const containerBottom = containerRect.bottom

  // 获取所有未加载的二级分类section
  const sections = container.querySelectorAll('.category-section')

  sections.forEach(section => {
    const sectionRect = section.getBoundingClientRect()
    const secondCategoryId = section.dataset.categoryId

    if (!secondCategoryId || loadedSecondCategoryIds.value.has(secondCategoryId)) {
      return
    }

    // 检查section是否在视口内或即将进入视口（提前加载）
    const isVisible = sectionRect.top < containerBottom + LOAD_THRESHOLD &&
      sectionRect.bottom > containerTop - LOAD_THRESHOLD

    if (isVisible) {
      loadThirdCategoryLazy(secondCategoryId)
    }
  })
}

// 监听窗口大小变化 - 防抖优化
const handleResize = () => {
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  resizeTimer = setTimeout(() => {
    calculateItemsPerRow()
    // 窗口大小变化时重新检查可见分类
    checkAndLoadVisibleCategories()
  }, 150)
}

// 页面加载时获取分类数据并计算布局
onMounted(() => {
  fetchCategories()
  nextTick(() => {
    calculateItemsPerRow()
    window.addEventListener('resize', handleResize)
  })
})

// 在组件卸载前移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }

  // 移除滚动监听器
  if (scrollListener && categoryContainer.value) {
    categoryContainer.value.removeEventListener('scroll', scrollListener)
  }
})

// 监听数据加载完成，重新计算布局
watch(loading, (newVal) => {
  if (!newVal) {
    nextTick(() => {
      calculateItemsPerRow()
    })
  }
})
</script>

<style scoped lang="less">
// 页面容器
.page-category {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: @bg-color-gray;
}

// 分类布局容器
.category-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
}

// 左侧分类导航
.category-sidebar {
  width: 110px;
  flex-shrink: 0;

  :deep(.van-sidebar) {
    width: 110px;
    height: 100%;
    overflow-y: auto;
    background-color: @bg-color-gray;
    text-align: center;
    .no-scrollbar();
  }

  :deep(.van-sidebar-item) {
    padding: 12px 6px;
    font-size: @font-size-13;
    color: @text-color-primary;
    transition: all 0.2s ease;

    &--select {
      color: @theme-color;
      font-weight: @font-weight-500;
      border-color: @theme-color;

      &::before {
        background-color: @theme-color;
      }
    }
  }
}

// 侧边栏骨架屏样式
.sidebar-skeleton {
  width: 110px;
  height: 100%;
  overflow-y: auto;
  background-color: @bg-color-gray;
  .no-scrollbar();
}

.skeleton-sidebar-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 6px;
}

.skeleton-sidebar-text {
  width: 60px;
  height: 14px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

// 右侧主内容区域
.category-main {
  flex: 1;
  overflow-y: auto;
  background-color: @bg-color-white;
  contain: layout style paint;
  .no-scrollbar();
}

// 骨架屏样式
.skeleton-wrapper {
  padding: @padding-page * 2;
}

.skeleton-section {
  margin-bottom: @padding-page * 2;
  padding: @padding-page * 2;
  background-color: @bg-color-white;
  border-radius: @radius-8;
  box-shadow: 0 2px 8px rgba(227, 227, 242, 0.5);

  &:last-child {
    margin-bottom: 0;
  }
}

// 骨架屏标题
.skeleton-section-title {
  width: 80px;
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 15px;
}

// 骨架屏分类网格
.skeleton-category-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}

.skeleton-category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  padding: 5px;
  margin-bottom: 15px;
  flex: 0 0 33.333%; // 一行显示3个
}

// 骨架屏图标
.skeleton-item-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8px;
  margin-bottom: 8px;
}

// 骨架屏名称
.skeleton-item-name {
  width: 50px;
  height: 12px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 6px;
}

// 骨架屏动画
@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}

// 分类内容区域
.category-sections {
  padding: @padding-page * 2;
}

.category-section {
  margin-bottom: @padding-page * 2;
  padding: @padding-page * 2;
  background-color: @bg-color-white;
  border-radius: @radius-8;
  box-shadow: 0 2px 8px rgba(227, 227, 242, 0.5);

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  margin: 0 0 10px 0;
  padding: 0;
  font-size: @font-size-14;
  font-weight: @font-weight-500;
  color: @text-color-primary;
  line-height: 1.4;
}

// 分类项目网格
.category-items {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  padding: 5px;
  margin-bottom: 15px;
  cursor: pointer;
  transition: transform 0.2s ease;
  will-change: transform;

  &:hover {
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
}

// 分类图标
.item-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 5px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  will-change: transform;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.2s ease;
  }
}

// 分类名称
.item-name {
  font-size: @font-size-12;
  color: @text-color-secondary;
  text-align: center;
  width: 100%;
  line-height: 1.2;
  word-break: break-word;
  .multi-ellipsis(2);
}

// 加载占位符样式
.loading-placeholder {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 0 -5px;

  .skeleton-item {
    flex: 0 0 calc(25% - 7.5px);
    margin-bottom: 15px;
  }
}

// 空状态样式
.empty-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;

  .empty-text {
    font-size: @font-size-13;
    color: @text-color-tertiary;
  }
}

// 占位区域样式
.placeholder-section {
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;

  .load-trigger {
    width: 100%;
    height: 20px;
    background: transparent;
  }
}

// 滚动优化
.category-main {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}
</style>
