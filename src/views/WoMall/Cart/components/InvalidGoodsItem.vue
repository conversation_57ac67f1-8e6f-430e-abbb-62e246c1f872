<template>
  <article class="invalid-goods-item">
    <button type="button" class="invalid-goods-item__checkbox" @click="handleToggleSelect"
      :aria-label="'选择商品：' + goodsInfo.name">
      <img :src="notSelectImg" alt="未选中" class="invalid-goods-item__checkbox-icon" width="18" height="18"
        loading="lazy" />
    </button>
    <div class="invalid-goods-item__content">
      <div class="invalid-goods-item__image-wrapper">
        <img :src="goodsInfo.imageUrl" :alt="goodsInfo.name" class="invalid-goods-item__image" width="75" height="75"
          loading="lazy" decoding="async" />
        <span class="invalid-goods-item__status-badge" aria-label="商品已失效">失效</span>
      </div>
      <div class="invalid-goods-item__info">
        <h3 class="invalid-goods-item__title">{{ goodsInfo.name }}</h3>
        <div class="invalid-goods-item__footer">
          <p class="invalid-goods-item__tips">{{ goodsInfo.tips }}</p>
          <WoButton size="small" class="invalid-goods-item__similar-btn" @click="handleLookSimilar(goodsInfo.goodsId)">
            看相似
          </WoButton>
        </div>
      </div>
    </div>
  </article>
</template>

<script setup>
import { computed, toRefs } from 'vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
// 导入图片
import notSelectImg from '@/static/images/not-selectable.png'

// 定义props
const props = defineProps({
  item: {
    type: Object,
    required: true
  },
})

// 使用toRefs解构props
const { item } = toRefs(props)

// 定义事件
const emit = defineEmits(['toggle-select', 'look-similar'])

// 计算商品信息
const goodsInfo = computed(() => {
  const currentSku = item.value?.goods?.skuList?.[0] || {}

  return {
    goodsId: currentSku.goodsId || '',
    skuId: currentSku.skuId || '',
    name: currentSku.name || '',
    imageUrl: currentSku.listImageUrl || '',
    tips: item.value?.tips || '此商品已不能购买'
  }
})

// 处理事件的方法
const handleToggleSelect = () => {
  emit('toggle-select', item.value)
}

const handleLookSimilar = (id) => {
  emit('look-similar', id)
}
</script>

<style scoped lang="less">
.invalid-goods-item {
  display: flex;
  align-items: center;
  position: relative;
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }

  &__checkbox {
    margin-right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;

    &:focus-visible {
      outline: 2px solid #007aff;
      outline-offset: 2px;
      border-radius: 2px;
    }
  }

  &__checkbox-icon {
    width: 18px;
    height: 18px;
    display: block;
  }

  &__content {
    display: flex;
    flex: 1;
    overflow: hidden;
  }

  &__image-wrapper {
    width: 75px;
    height: 75px;
    border-radius: @radius-4;
    overflow: hidden;
    margin-right: 10px;
    position: relative;
    flex-shrink: 0;
  }

  &__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.4;
    display: block;
  }

  &__status-badge {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    border-radius: @radius-10;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: @font-size-12;
    z-index: 2;
    height: 20px;
    width: 43px;
    line-height: 20px;
  }

  &__info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-width: 0;
  }

  &__title {
    font-size: @font-size-13;
    color: @text-color-tertiary;
    line-height: 1.5;
    margin: 0 0 5px 0;
    font-weight: normal;
    .multi-ellipsis(2);
  }

  &__footer {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: 8px;
  }

  &__tips {
    font-size: @font-size-12;
    color: @text-color-tertiary;
    margin: 0;
    flex: 1;
    min-width: 0;
  }

  &__similar-btn {
    flex-shrink: 0;
  }
}
</style>
