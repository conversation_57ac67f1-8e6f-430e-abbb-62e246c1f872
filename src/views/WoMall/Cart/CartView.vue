<template>
  <MainLayout>
    <!-- 未登录状态 -->
    <section class="cart-login" v-if="!isLogin">
      <img class="cart-login__image" src="./assets/no-goods.png" alt="购物车空空如也" loading="lazy" />
      <p class="cart-login__text">登录后可同步购物车中商品</p>
      <WoButton type="gradient" size="medium" @click="handleLogin">
        立即登录
      </WoButton>
    </section>

    <!-- 已登录状态 -->
    <section v-else class="cart">
      <!-- 地址选择区域 -->
      <header class="cart-header">
        <button class="cart-header__address" @click="handleSelectAddress" type="button">
          <img src="@/static/images/location.png" alt="地址标识" class="cart-header__location-icon" loading="lazy" />
          <span class="cart-header__address-text">{{ addressDisplay }}</span>
          <img src="@/static/images/arrow-right-black.png" alt="选择地址" class="cart-header__arrow-icon" loading="lazy" />
        </button>
        <button class="cart-header__edit" @click="handleEditToggle" type="button">
          {{ isEditMode ? '完成' : '编辑' }}
        </button>
      </header>

      <!-- 主要内容区域 -->
      <main class="cart-main">
        <!-- 加载状态 -->
        <section v-if="cartStore.getCartLoadingStatus === CART_QUERY_STATUS.LOADING" class="cart-loading">
          <WoCard class="cart-loading__item" v-for="n in 2" :key="n">
            <div class="cart-loading__content">
              <div class="cart-loading__checkbox"></div>
              <div class="cart-loading__image"></div>
              <div class="cart-loading__info">
                <div class="cart-loading__header">
                  <div class="cart-loading__name"></div>
                  <div class="cart-loading__quantity"></div>
                </div>
                <div class="cart-loading__tags">
                  <div class="cart-loading__spec-tag"></div>
                  <div class="cart-loading__spec-tag"></div>
                </div>
                <div class="cart-loading__price"></div>
              </div>
            </div>
          </WoCard>
        </section>

        <!-- 空购物车状态 -->
        <section
          v-else-if="!cartStore.hasValidGoods && !cartStore.hasInvalidGoods && cartStore.getCartLoadingStatus === CART_QUERY_STATUS.SUCCESS"
          class="cart-empty">
          <div class="cart-empty__content">
            <img src="./assets/no-goods.png" alt="购物车空空如也" class="cart-empty__image" loading="lazy" />
            <p class="cart-empty__text">购物车空空如也，去逛逛吧~</p>
            <WoButton type="secondary" size="medium" class="cart-empty__button" @click="$router.push('/home')">
              去逛逛{{ getMallName() }}
            </WoButton>
          </div>
        </section>

        <!-- 购物车商品列表 -->
        <section v-else class="cart-goods">
          <!-- 有效商品 -->
          <div class="cart-goods__valid" v-for="(group, groupIndex) in cartStore.getCartValidList" :key="groupIndex">
            <ValidGoodsItem v-for="(item, itemIndex) in group.goodsList" :key="item.cartSkuId" :item="item"
              :ref-key="`goodsItem_${groupIndex}_${itemIndex}`" :is-edit-mode="isEditMode"
              :is-edit-selected="isEditModeItemSelected(item)" @toggle-select="handleToggleItemSelect"
              @show-stepper="showStepper" @quantity-change="handleQuantityChange" @look-similar="handleLookSimilar"
              @delete-item="handleDeleteItem" @close-menu="closeLongPressMenu" @swipe-open="handleSwipeOpen"
              @swipe-close="handleSwipeClose" @set-ref="(el, key) => { goodsItemRefs[key] = el }"
              @long-press="handleLongPress" @gift-click="handleGiftClick" />
          </div>

          <!-- 失效商品 -->
          <WoCard v-if="cartStore.hasInvalidGoods" class="cart-goods__invalid">
            <header class="cart-goods__invalid-header">
              <h3 class="cart-goods__invalid-title">{{ invalidGoodsCount }}件失效商品</h3>
              <button class="cart-goods__invalid-action" @click="handleClearInvalidGoods" type="button">
                一键清空
              </button>
            </header>
            <div class="cart-goods__invalid-list">
              <InvalidGoodsItem v-for="item in cartStore.getCartInvalidList[0]?.goodsList" :key="item.cartSkuId"
                :item="item" :invalid-count="invalidGoodsCount" @toggle-select="handleToggleItemSelect"
                @look-similar="handleLookSimilar" />
            </div>
          </WoCard>
        </section>
      </main>


      <!-- 底部结算栏占位符 -->
      <WoActionBarPlaceholder />
    </section>
    <!-- 底部结算栏 -->
    <WoActionBar :bottom="49"
      v-if="cartStore.getCartLoadingStatus === CART_QUERY_STATUS.SUCCESS && cartStore.hasValidGoods">
      <footer class="cart-footer">
        <!-- 编辑模式 -->
        <template v-if="isEditMode">
          <button class="cart-footer__select-all" @click="handleEditModeToggleAll" type="button">
            <img :src="isEditModeAllSelected ? woSelectImg : noSelectImg" alt="全选" class="cart-footer__checkbox-icon"
              loading="lazy" />
            <span class="cart-footer__select-text">全选</span>
          </button>
          <div class="cart-footer__edit-info">
            已选{{ tempSelectedItems.size }}件
          </div>
          <WoButton type="gradient" size="medium" class="cart-footer__action-btn"
            :disabled="tempSelectedItems.size === 0" @click="handleEditModeDelete">
            删除
          </WoButton>
        </template>

        <!-- 正常模式 -->
        <template v-else>
          <button class="cart-footer__select-all" @click="handleToggleAllSelect" type="button">
            <img :src="cartStore.isSelectAll ? woSelectImg : noSelectImg" alt="全选" class="cart-footer__checkbox-icon"
              loading="lazy" />
            <span class="cart-footer__select-text">全选</span>
          </button>
          <div class="cart-footer__price-info">
            已选{{ cartStore.selectCountAll }}件，合计
            <PriceDisplay :price="cartStore.selectTotalPrice" size="medium" color="orange" />
          </div>
          <WoButton type="gradient" size="medium" class="cart-footer__action-btn"
            :disabled="!cartStore.hasSelectedGoods" @click="handleCheckout">
            结算
          </WoButton>
        </template>
      </footer>
    </WoActionBar>
  </MainLayout>

  <!-- 添加礼品弹窗 -->
  <GiftDisplayPopup v-model:visible="giftPopupVisible" :gift-list="giftList" :goods-num="currentGoodsNum" />

  <!-- 地址快速选择弹窗 -->
  <AddressQuickSelectionPopup v-model:visible="addressPopupVisible" @select="handleAddressSelect" />

  <!--  TODO 精选推荐没有加-->
</template>

<script setup>
// ==================== 依赖导入 ====================
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showLoadingToast, closeToast, showToast } from 'vant'

// Store
import { useNewCartStore, CART_QUERY_STATUS } from '@/store/modules/newCart'
import { useUserStore } from '@/store/modules/user'

// 组件
import WoCard from '@components/WoElementCom/WoCard.vue'
import PriceDisplay from '@/components/Common/PriceDisplay.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import WoActionBarPlaceholder from '@components/WoElementCom/WoActionBarPlaceholder.vue'
import GiftDisplayPopup from '@/components/Common/GiftDisplayPopup.vue'
import AddressQuickSelectionPopup from '@/components/Address/AddressQuickSelectionPopup.vue'
import ValidGoodsItem from '@views/WoMall/Cart/components/ValidGoodsItem.vue'
import InvalidGoodsItem from '@views/WoMall/Cart/components/InvalidGoodsItem.vue'
import MainLayout from '@components/Common/MainLayout/MainLayout.vue'

// 工具和API
import { getMallName, getBizCode } from '@utils/curEnv.js'
import { useAlert } from '@/hooks/index.js'
import { similarity, getGiftDetails, checkOrderSku, jdAddressCheck } from '@/api/index.js'
import { BatchRequest } from '@/utils/tools.js'
import { buyProductCart, buyProductCartSession } from '@/utils/storage.js'

// 图片资源
import noSelectImg from '@/static/images/no-select.png'
import woSelectImg from '@/static/images/wo-select.png'

// ==================== 基础设置 ====================
const $alert = useAlert()
const router = useRouter()
const cartStore = useNewCartStore()
const userStore = useUserStore()

// ==================== 响应式数据 ====================
// 弹窗状态
const giftPopupVisible = ref(false)
const addressPopupVisible = ref(false)

// 礼品相关数据
const giftList = ref([])
const currentGoodsNum = ref(0)
const cacheSkuGiftDetailsList = new Map() // 赠品详情缓存

// 编辑模式相关
const isEditMode = ref(false)
const tempSelectedItems = ref(new Set()) // 临时选择的商品ID集合

// 长按操作相关
const longPressedItem = ref(null)
const goodsItemRefs = ref({})

// ==================== 计算属性 ====================
// 登录状态
const isLogin = computed(() => userStore.isLogin)

// 地址显示
const addressDisplay = computed(() => {
  const addr = userStore.curAddressInfo
  if (!addr) return '请选择收货地址'
  return addr.addrDetail || `${addr.provinceName}${addr.cityName}${addr.countyName}`
})

// 失效商品数量
const invalidGoodsCount = computed(() => {
  return cartStore.getCartInvalidList[0]?.goodsList?.length || 0
})

// 编辑模式下是否全选
const isEditModeAllSelected = computed(() => {
  const totalItems = cartStore.getCartValidList.reduce((total, group) => {
    return total + group.goodsList.length
  }, 0)
  return totalItems > 0 && tempSelectedItems.value.size === totalItems
})

// ==================== 初始化相关 ====================
/**
 * 地址检查方法
 * 检查用户地址是否符合配送要求
 */
const addressCheck = async () => {
  const [err, json] = await jdAddressCheck()
  if (err) {
    showToast(err.msg)
    return
  }
  if (!json) {
    await $alert({
      message: '由于物流配送地址库规则升级，收货地址需要精确到街道，请您重新设置地址，按指引操作及保存',
      confirmButtonText: '修改地址',
      showCancelButton: true,
      cancelButtonText: '取消',
      onConfirmCallback: () => {
        router.push({
          name: 'address-edit',
          query: {
            addrId: userStore.curAddressInfo.addressId,
            isInvalid: '1'
          }
        })
      },
      onCancelCallback: () => { }
    })
    return false
  }
  return true
}

/**
 * 初始化商品属性
 * 为购物车商品添加UI交互所需的属性
 */
const initializeGoodsProperties = () => {
  cartStore.getCartValidList.forEach(group => {
    group.goodsList.forEach(item => {
      if (item.stepperVisible === undefined) item.stepperVisible = false
      if (item.isSwipeOpen === undefined) item.isSwipeOpen = false
      if (item.showLongPressMenu === undefined) item.showLongPressMenu = false
    })
  })
}

/**
 * 购物车初始化主函数
 * 负责登录状态检查、地址验证、购物车数据加载等
 */
const initializeCart = async () => {
  try {
    // 查询登录状态
    await userStore.queryLoginStatus()

    // 只有登录了才执行这些操作
    if (userStore.isLogin) {
      // 检查地址信息
      showLoadingToast()
      await addressCheck()
      closeToast()

      // 加载购物车数据
      await cartStore.query()

      // 初始化商品的属性
      initializeGoodsProperties()
    }
  } catch (error) {
    console.error('初始化购物车失败:', error)
  }
}

// ==================== 登录相关 ====================
/**
 * 处理登录
 */
const handleLogin = () => {
  userStore.login()
}

// ==================== 地址相关 ====================
/**
 * 选择地址处理函数
 */
const handleSelectAddress = () => {
  addressPopupVisible.value = true
}

/**
 * 处理地址选择
 * 地址选择后重新初始化购物车
 */
const handleAddressSelect = async () => {
  try {
    // 重新初始化购物车（重新调用初始化函数）
    await initializeCart()
  } catch (error) {
    console.error('更新地址失败:', error)
    showToast('更新地址失败')
  }
}

// ==================== 商品选择相关 ====================
/**
 * 切换单个商品选中状态
 * 根据当前模式（编辑/正常）执行不同的选择逻辑
 */
const handleToggleItemSelect = async (item) => {
  if (isEditMode.value) {
    // 编辑模式下使用临时选择，不调用接口
    handleEditModeSelect(item)
  } else {
    // 正常模式下调用接口更新选中状态
    try {
      const newSelectState = item.selected !== 'true'
      await cartStore.updateGoodsSelectMuti([{
        goodsId: item.cartGoodsId,
        skuId: item.cartSkuId,
        select: newSelectState
      }])
    } catch (error) {
      console.error('更新商品选中状态失败:', error)
    }
  }
}

/**
 * 全选/取消全选
 */
const handleToggleAllSelect = async () => {
  try {
    await cartStore.checkedAllValid()
  } catch (error) {
    console.error('全选操作失败:', error)
  }
}

// ==================== 商品数量相关 ====================
/**
 * 显示数量调节器
 */
const showStepper = (item) => {
  item.stepperVisible = true
}

/**
 * 处理商品数量变化
 * 包含库存不足的错误处理
 */
const handleQuantityChange = async (item) => {
  try {
    const error = await cartStore.updateGoodsNum({
      goodsId: item.cartGoodsId,
      skuId: item.cartSkuId,
      goodsNum: item.skuNum
    })

    if (error) {
      // 处理库存不足等错误情况
      if (error.code === 'FE2001' && error.stock !== undefined) {
        // 库存不足，更新本地数量为实际库存
        const targetItem = cartStore.getCartValidList
          .flatMap(group => group.goodsList)
          .find(goods => goods.cartGoodsId === item.cartGoodsId && goods.cartSkuId === item.cartSkuId)

        if (targetItem) {
          targetItem.skuNum = error.stock
        }
      }
      showToast(error.msg || '更新商品数量失败')
    }
  } catch (error) {
    console.error('更新商品数量异常:', error)
    showToast('更新商品数量失败')
  }
}

// ==================== 商品删除相关 ====================
/**
 * 处理删除单个商品
 */
const handleDeleteItem = async (item) => {
  try {
    await cartStore.removeMuti(item)
  } catch (error) {
    console.error('删除商品失败:', error)
  }
}

/**
 * 处理清空失效商品
 */
const handleClearInvalidGoods = async () => {
  try {
    const invalidGoods = cartStore.getCartInvalidList[0]?.goodsList || []
    const deleteGoodsList = invalidGoods.map(item => ({
      goodsId: item.cartGoodsId,
      skuId: item.cartSkuId
    }))

    await cartStore.removeInvalidGoods({ deleteGoodsList })
  } catch (error) {
    console.error('清空失效商品失败:', error)
  }
}

// ==================== 商品交互相关 ====================
/**
 * 处理滑动打开事件
 */
const handleSwipeOpen = (item) => {
  item.isSwipeOpen = true
}

/**
 * 处理滑动关闭事件
 */
const handleSwipeClose = (item) => {
  item.isSwipeOpen = false
}

/**
 * 处理长按事件
 * 显示长按菜单，同时关闭其他商品的长按菜单
 */
const handleLongPress = (item) => {
  // 先关闭所有其他项的长按菜单
  cartStore.getCartValidList.forEach(group => {
    group.goodsList.forEach(good => {
      if (good !== item) {
        good.showLongPressMenu = false
      }
    })
  })

  // 设置当前长按的项
  longPressedItem.value = item
  // 显示当前项的长按菜单
  item.showLongPressMenu = true
}

/**
 * 关闭长按菜单
 */
const closeLongPressMenu = () => {
  if (longPressedItem.value) {
    longPressedItem.value.showLongPressMenu = false
    longPressedItem.value = null
  }
}

// ==================== 编辑模式相关 ====================
/**
 * 切换编辑模式
 */
const handleEditToggle = () => {
  if (isEditMode.value) {
    // 退出编辑模式，清除临时选择
    isEditMode.value = false
    tempSelectedItems.value.clear()
  } else {
    // 进入编辑模式
    isEditMode.value = true
    tempSelectedItems.value.clear()
  }
}

/**
 * 编辑模式下的商品选择
 */
const handleEditModeSelect = (item) => {
  const itemId = `${item.cartGoodsId}_${item.cartSkuId}`
  if (tempSelectedItems.value.has(itemId)) {
    tempSelectedItems.value.delete(itemId)
  } else {
    tempSelectedItems.value.add(itemId)
  }
}

/**
 * 编辑模式下的全选/取消全选
 */
const handleEditModeToggleAll = () => {
  if (tempSelectedItems.value.size === 0) {
    // 全选
    cartStore.getCartValidList.forEach(group => {
      group.goodsList.forEach(item => {
        const itemId = `${item.cartGoodsId}_${item.cartSkuId}`
        tempSelectedItems.value.add(itemId)
      })
    })
  } else {
    // 取消全选
    tempSelectedItems.value.clear()
  }
}

/**
 * 编辑模式下的删除操作
 */
const handleEditModeDelete = async () => {
  if (tempSelectedItems.value.size === 0) {
    return
  }

  try {
    // 使用 $alert 弹出确认框
    await $alert({
      title: '确认删除',
      message: `确定要删除选中的 ${tempSelectedItems.value.size} 件商品吗？`,
      showCancelButton: true,
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      onConfirmCallback: async () => {
        const deleteGoodsList = []
        tempSelectedItems.value.forEach(itemId => {
          const [goodsId, skuId] = itemId.split('_')
          deleteGoodsList.push({
            goodsId,
            skuId
          })
        })

        // 调用删除接口
        await cartStore.removeMuti(deleteGoodsList)

        // 退出编辑模式
        isEditMode.value = false
        tempSelectedItems.value.clear()
      }
    })
  } catch (error) {
    console.error('删除商品失败:', error)
  }
}

/**
 * 计算编辑模式下的选中状态
 */
const isEditModeItemSelected = (item) => {
  const itemId = `${item.cartGoodsId}_${item.cartSkuId}`
  return tempSelectedItems.value.has(itemId)
}

// ==================== 礼品相关 ====================
/**
 * 获取赠品详情
 * 支持缓存机制，避免重复请求
 */
const getGiftsDetails = async (goodsInfo, giftList) => {
  const batchRequest = new BatchRequest()
  const tempGiftDetailsList = []
  const skuId = goodsInfo.goods?.skuList?.[0]?.skuId
  const supplierCode = goodsInfo.goods?.supplierCode || 'jd_yg'

  if (!skuId || !giftList || giftList.length === 0) {
    return []
  }

  // 检查缓存
  if (cacheSkuGiftDetailsList.has(skuId)) {
    return cacheSkuGiftDetailsList.get(skuId)
  }

  // 批量请求赠品详情
  giftList.forEach((item, index) => {
    const promise = getGiftDetails({
      supplierSkuId: item.giftId,
      supplierCode: supplierCode
    }).then(([err, json]) => {
      if (err) {
        tempGiftDetailsList.splice(index, 0, {})
      } else {
        tempGiftDetailsList.splice(index, 0, {
          ...json,
          giftType: item.giftType,
          giftNum: item.giftNum,
          belongToSkuMaxNum: item.belongToSkuMaxNum,
          belongToSkuMinNum: item.belongToSkuMinNum
        })
      }
    })

    batchRequest.push(promise)
  })

  return new Promise((resolve) => {
    batchRequest.onComplete = () => {
      // 缓存结果
      cacheSkuGiftDetailsList.set(skuId, tempGiftDetailsList)
      resolve(tempGiftDetailsList)
    }
  })
}

/**
 * 处理礼品点击事件
 * 获取礼品详情并显示弹窗
 */
const handleGiftClick = async (data) => {
  const { goods: goodsInfo, giftList: goodsInfoGiftList } = data

  try {
    showLoadingToast()

    // 设置当前商品数量
    currentGoodsNum.value = goodsInfo.skuNum || 0

    // 获取赠品详情
    const giftDetails = await getGiftsDetails(goodsInfo, goodsInfoGiftList)

    // 更新礼品列表数据
    giftList.value = giftDetails.filter(item => item && Object.keys(item).length > 0)

    closeToast()

    // 显示弹窗
    giftPopupVisible.value = true

  } catch (error) {
    closeToast()
    console.error('获取赠品详情失败:', error)
    // 即使失败也显示弹窗，但数据为空
    giftList.value = []
    currentGoodsNum.value = goodsInfo.skuNum || 0
    giftPopupVisible.value = true
  }
}

// ==================== 相似商品相关 ====================
/**
 * 处理查看相似商品
 * 根据商品ID查找相似商品分类并跳转
 */
const handleLookSimilar = async (item) => {
  try {
    showLoadingToast()

    // 获取商品ID，支持直接传入ID或者传入商品对象
    const goodsId = typeof item === 'string' ? item : item.cartGoodsId || item.goodsId

    const [err, json] = await similarity({
      goodsId,
      bizCode: getBizCode('GOODS')
    })

    closeToast()

    if (!err && json) {
      // 如果有具体的相似分类，就去具体的分类列表
      router.push(`/goodslist/${json}`)
    } else {
      // 未查到具体的分类，就去分类首页
      router.push({ path: '/category' })
    }
  } catch (error) {
    closeToast()
    console.error('查看相似商品失败:', error)
    // 出错时也跳转到分类首页
    router.push({ path: '/category' })
  }
}

// ==================== 结算相关 ====================
/**
 * 结算处理函数
 * 验证选中商品、构建订单数据、预检查订单并跳转
 */
const handleCheckout = async () => {
  // 检查是否有选中商品
  if (cartStore.selectCountAll < 1) {
    showToast('请选择下单商品')
    return
  }

  try {
    // 构建购买商品列表
    const buyGoodsList = []
    cartStore.getCartValidList.forEach(group => {
      group.goodsList.forEach(item => {
        if (item.selected === 'true') {
          buyGoodsList.push({
            cartGoodsId: item.cartGoodsId,
            cartSkuId: item.cartSkuId,
            skuNum: item.skuNum,
            supplierCode: item.supplierCode,
            nowPrice: item.nowPrice
          })
        }
      })
    })

    // 检查购买商品列表是否为空
    if (!buyGoodsList || buyGoodsList.length <= 0) {
      console.error('buyProductCart购车页面buyGoodsList缓存数据为空')
      showToast('请选择下单商品')
      return
    }

    // 缓存购买商品列表
    buyProductCart.set(buyGoodsList)
    buyProductCartSession.set(buyGoodsList)

    // 准备地址信息
    const info = userStore.curAddressInfo
    if (!info) {
      showToast('请先选择收货地址')
      return
    }

    const addressInfo = JSON.stringify({
      provinceId: info.provinceId,
      provinceName: info.provinceName,
      cityId: info.cityId,
      cityName: info.cityName,
      countyId: info.countyId,
      countyName: info.countyName,
      townId: info.townId,
      townName: info.townName
    })

    const bizCode = getBizCode('ORDER')

    // 显示加载状态
    showLoadingToast()

    // 调用预检查接口
    const [err] = await checkOrderSku({
      bizCode,
      addressInfo: addressInfo,
      buyGoodsList: JSON.stringify(buyGoodsList)
    })

    closeToast()

    if (!err) {
      // 验证成功，跳转到订单确认页
      router.push('/orderconfirm')
    } else {
      // 验证失败，显示错误信息
      showToast(err.msg || '订单验证失败')

      // 延迟刷新购物车数据
      setTimeout(async () => {
        try {
          showLoadingToast()
          await cartStore.query()
          closeToast()
        } catch (refreshError) {
          closeToast()
          console.error('刷新购物车失败:', refreshError)
        }
      }, 1500)
    }
  } catch (error) {
    closeToast()
    console.error('结算处理失败:', error)
    showToast('结算失败，请重试')
  }
}

// ==================== 生命周期 ====================
onMounted(() => {
  initializeCart()
})
</script>
<style scoped lang="less">
// ==================== 购物车页面样式 - 采用BEM命名规范 ====================

// 未登录状态样式
.cart-login {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px 20px;
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  position: relative;
  // 性能优化：使用contain属性
  contain: layout style paint;

  &__image {
    width: 180px;
    height: 160px;
    margin-bottom: 24px;
    opacity: 0.9;
    filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.1));
    position: relative;
    z-index: 1;
    // 优化图片渲染
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    // 添加轻微的动画效果
    animation: cart-float 3s ease-in-out infinite;
  }

  &__text {
    font-size: @font-size-13;
    color: @text-color-secondary;
    margin-bottom: 10px;
    line-height: 1.5;
    font-weight: 400;
    position: relative;
    z-index: 1;
  }

  &__button {
    width: 200px;
    height: 44px;
    position: relative;
    z-index: 1;
    // 增强按钮的视觉效果
    box-shadow: 0 4px 16px rgba(255, 107, 53, 0.25);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:active {
      transform: translateY(1px);
      box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
    }

    // 添加按钮悬停效果（移动端可能不生效，但不影响）
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 6px 20px rgba(255, 107, 53, 0.3);
    }
  }
}

// 购物车主容器
.cart {
  height: 100%;
  padding: 10px 10px 0 10px;
  box-sizing: border-box;
  background: #F8F9FA;
  user-select: none;
  overflow: auto;
  // 性能优化
  contain: layout style;
}

// 购物车头部（地址选择区域）
.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  gap: 15px;

  &__address {
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    // 优化点击体验
    touch-action: manipulation;
  }

  &__location-icon {
    margin-right: 5px;
    width: 11px;
    height: 12px;
    flex-shrink: 0;
    // 优化图片渲染
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  &__address-text {
    font-size: @font-size-12;
    color: @text-color-primary;
    font-weight: @font-weight-400;
    text-align: left;
    .ellipsis();
  }

  &__arrow-icon {
    margin-left: 5px;
    width: 5px;
    height: 9px;
    flex-shrink: 0;
    // 优化图片渲染
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  &__edit {
    flex-shrink: 0;
    font-size: @font-size-12;
    font-weight: @font-weight-600;
    color: @theme-color;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    // 优化点击体验
    touch-action: manipulation;
    transition: opacity 0.2s ease;

    &:active {
      opacity: 0.7;
    }
  }
}

// 购物车主要内容区域
.cart-main {
  flex: 1;
  overflow: hidden;
}

// 加载状态样式 - 匹配ValidGoodsItem布局
.cart-loading {
  &__item {
    margin-bottom: 10px;
  }

  &__content {
    display: flex;
    align-items: center;
    position: relative;
  }

  &__checkbox {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    margin-right: 5px;
    flex-shrink: 0;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  &__image {
    width: 75px;
    height: 75px;
    border-radius: @radius-4;
    margin-right: 10px;
    flex-shrink: 0;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  &__info {
    flex: 1;
    overflow: hidden;
    min-width: 0;
  }

  &__header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 5px;
    gap: 10px;
  }

  &__name {
    flex: 1;
    height: 16px;
    border-radius: 4px;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  &__quantity {
    width: 25px;
    height: 16px;
    border-radius: 4px;
    flex-shrink: 0;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  &__tags {
    display: flex;
    gap: 5px;
    margin-bottom: 5px;
  }

  &__spec-tag {
    width: 40px;
    height: 17px;
    border-radius: @radius-2;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  &__price {
    width: 60px;
    height: 18px;
    border-radius: 4px;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }
}

// 骨架屏动画
@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

// 空购物车状态样式
.cart-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
  // 性能优化
  contain: layout style paint;

  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  &__image {
    width: 180px;
    height: 160px;
    margin-bottom: 10px;
    // 优化图片渲染
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  &__text {
    font-size: @font-size-14;
    color: @text-color-tertiary;
    margin-bottom: 10px;
    line-height: 1.5;
  }

  &__button {
    width: 200px;
  }
}

// 购物车商品列表样式
.cart-goods {
  &__valid {
    margin-bottom: 10px;
  }

  &__invalid {
    margin-top: 10px;
  }

  &__invalid-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  &__invalid-title {
    font-size: @font-size-14;
    font-weight: @font-weight-600;
    color: @text-color-primary;
    margin: 0; // 重置h3默认margin
  }

  &__invalid-action {
    font-size: @font-size-14;
    color: @theme-color;
    font-weight: @font-weight-600;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    // 优化点击体验
    touch-action: manipulation;
    transition: opacity 0.2s ease;

    &:active {
      opacity: 0.7;
    }
  }

  &__invalid-list {
    // 为失效商品列表预留样式
  }
}

// 购物车底部结算栏样式
.cart-footer {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 10px;

  &__select-all {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    // 优化点击体验
    touch-action: manipulation;
    transition: opacity 0.2s ease;

    &:active {
      opacity: 0.7;
    }
  }

  &__checkbox-icon {
    width: 18px;
    height: 18px;
    margin-right: 5px;
    // 优化图片渲染
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  &__select-text {
    font-size: @font-size-13;
    color: @text-color-primary;
  }

  &__price-info {
    flex: 1;
    font-size: @font-size-14;
    color: @text-color-primary;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 4px;

    .price {
      font-size: @font-size-16;
      font-weight: @font-weight-600;
      color: @theme-color;
    }
  }

  &__edit-info {
    flex: 1;
    font-size: @font-size-14;
    color: @text-color-primary;
    display: flex;
    align-items: center;
  }

  &__action-btn {
    flex-shrink: 0;
  }
}

// 浮动动画
@keyframes cart-float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-8px);
  }
}

// 针对移动端的优化
@media (max-width: 768px) {
  .cart-header {
    &__address-text {
      font-size: @font-size-11;
    }

    &__edit {
      font-size: @font-size-11;
    }
  }

  .cart-footer {
    &__select-text {
      font-size: @font-size-12;
    }

    &__price-info {
      font-size: @font-size-13;
    }

    &__edit-info {
      font-size: @font-size-13;
    }
  }
}

// 性能优化：为主要容器添加GPU加速
.cart,
.cart-login,
.cart-empty {
  transform: translateZ(0);
  backface-visibility: hidden;
}

// 减少重绘：为动画元素使用will-change
.cart-login__image {
  will-change: transform;
}

// 优化滚动性能
.cart {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}
</style>
