<template>
  <div class="goods-detail">
    <!-- 顶部吸顶  商品  详情 -->
    <ul ref="detailTabHeader" class="sticky-goods-header">
      <li :class="goodsDetailTab === 'introduce' ? 'active' : ''" @click="setGoodsDetailTab('introduce')">
        商品
      </li>
      <li :class="goodsDetailTab === 'description' ? 'active' : ''" @click="setGoodsDetailTab('description')">
        详情
      </li>
    </ul>
    <!-- 右上角 ... 功能 -->
    <Dropdown class="top-right-menus" v-show="showMenus" @share="onDropdownShare">
      <div class="circle">
        <span class="dot" />
        <span class="dot" />
        <span class="dot" />
      </div>
    </Dropdown>
    <div ref="scrollWrapper" class="scroll-list-wrap" v-on:scroll="scrollHandler">
      <div ref="goodsIntro">
        <div :class="{ 'slider-wrapper': true }">
          <Slider v-if="sliderItems.length > 0" :swipeItems="sliderItems" />
          <div v-else class="occupying-block" :style="{ width: innerWidth + 'px', height: innerWidth + 'px' }"></div>
        </div>
        <div class="goods-desc">
          <p class="goods-title" v-if="currSku.name">
            <span v-if="isShowBizCode" class="mall-icon"></span>
            <!--            <span v-if="isShowBizCode" class="goods-suppliername"> {{ getSupplierCodeToName(currSku.supplierCode) }}-->
            <!--            </span>-->
            <span class="goods-name">{{ currSku.name }}</span>
          </p>
          <div class="goods-price-sales">
            <p class="goods-price">
              <span class="curr-price orange-text" v-if="promotionPrice">
                ￥<i>{{ promotionPrice[0] }}</i> .{{ promotionPrice[1] }}
              </span>
              <span class="del-price grey-text" v-if="originalPrice">￥{{ originalPrice }}</span>
              <span class="goods-price-reduce" v-if="reducePrice > 0">
                预计到手价格 ￥<i>{{ finalPrice }}</i>
              </span>
            </p>
          </div>
        </div>
        <div class="devide-blank" />
        <div class="goods-marketing"
          v-if="marketTemplatesType1 && marketTemplatesType1.length > 0 && marketTemplatesType1[0].upImage">
          <img @click="marketingBtn()" :src="marketTemplatesType1[0].upImage" alt="" />
        </div>
        <div class="goods-config">
          <div class="cell-wrapper borderd">
            <Cell title="已选" arrow :clickFn="chooseSpecification" class="cell">
              <template slot="left">
                <span class="cell-desc">{{ paramString }}件</span>
              </template>
            </Cell>
          </div>
          <div class="cell-wrapper">
            <Cell title="送至" arrow :clickFn="chooseAddress" class="cell">
              <template slot="left">
                <span class="cell-desc">{{ locationText }}</span>
              </template>
            </Cell>
            <template v-if="isShowLogisticsServices">
              <div v-if="isJD && logisticsServicesInfo.predictContent" class="delivery-logistics-info">
                <img v-if="logisticsServicesInfo.logisticsType === 1 && isJD" alt="" class="logistics-icon"
                  src="./assets/jd-log.png" />
                <img
                  v-if="(logisticsServicesInfo.logisticsType === 0 || logisticsServicesInfo.logisticsType === 2) && isJD"
                  alt="" class="logistics-icon" src="./assets/sf-log.png" />
                <p v-if="isJD && logisticsServicesInfo.predictContent" class="logistics-info"
                  v-html="logisticsServicesInfo.predictContent"></p>
              </div>
              <div v-else class="delivery-logistics-info">
                <img alt="" class="logistics-icon" src="./assets/sf-log.png" />
                <p class="logistics-info">预计48小时之内发货</p>
              </div>
            </template>
          </div>
        </div>
        <div class="devide-blank"
          v-if="marketTemplatesType4 && marketTemplatesType4.length > 0 && getBizCode() === 'ziying'" />
        <div class="promotion-activity"
          v-if="marketTemplatesType4 && marketTemplatesType4.length > 0 && getBizCode() === 'ziying'">
          <div class="promotion-activity-title">
            优惠活动
          </div>
          <div class="promotion-activity-content">
            <div class="promotion-item" v-for="(item, index) in marketTemplatesType4" :key="index"
              @click="goToPromotionDetail(item)">
              <img :src="item.activityImage" alt="优惠活动" class="promotion-heart-img">
              <div class="promotion-content">
                {{ item.activityName }}
              </div>
              <div class="promotion-arrow">
                <span class="promotion-desc">{{ item.activityDec }}</span>
                <i class="arrow-icon"></i>
              </div>
            </div>
          </div>
        </div>
        <div class="devide-blank" />
        <div class="cell-wrapper cell-wrapper-limit" v-if="isShowActivity">
          <Cell title="活动" arrow class="cell">
            <template slot="left">
              <p class="cell-desc-all cell-desc-limit">
                <span class="limit-icon" v-if="xgObj.isXg && xgObj.limitText">{{ xgObj.limitText }}</span>
                <span class="limit-icon" v-if="lowestBuyObj.isLowestBuy">{{ lowestBuyObj.lowestBuyText }}</span>
              </p>
            </template>
            <template slot="right">
              <p class="cell-desc-all cell-desc-limit" @click="showGiftDetailList" v-if="currSkuGiftNum">
                <span class="limit-text">赠品</span>
              </p>
            </template>
          </Cell>
        </div>
        <div class="devide-blank" v-if="isShowActivity" />
        <div v-if="isJD && logisticsServicesInfo.returnRuleStr" class="cell-wrapper">
          <Cell align="top" class="cell" title="服务">
            <template slot="left">
              <p class="cell-desc-all">
                {{ logisticsServicesInfo.returnRuleStr }}
              </p>
            </template>
          </Cell>
        </div>
        <div v-if="isJD && logisticsServicesInfo.returnRuleStr" class="devide-blank" />
        <div class="cell-wrapper">
          <Cell title="保障" align="top" class="cell">
            <template slot="left">
              <p class="cell-desc-all">
                <span class="line">店铺售后 <span>由&nbsp;<span class="red">{{ currSku.merchantName
                      }}</span>&nbsp;提供服务</span></span>
              </p>
            </template>
          </Cell>
        </div>
        <div class="devide-blank" />
      </div>
      <!-- 商品详情 -->
      <div class="goods-detail-view">
        <!-- 商品信息-->
        <goods-introduce :currentSKU="currSku" />
        <!-- 规格参数-->
        <goods-parameter v-show="detailTab === 'parameter'" :items="goodsParam" />
      </div>
    </div>

    <!-- 商品规格选择 -->
    <goods-choose-board :currSkuNum="goodsNum" :specsList="specsList" :curSpecs="curSpecs"
      :curDisabledSpecs="curDisabledSpecs" :currSku="currSku" :stockState="stockState"
      :loanProductList="loanProductList" :popType="popType" :goodsMultiState="goodsChooseBoardButtonDisabledStatus"
      :xgObj="xgObj" :lowestBuyObj="lowestBuyObj" :detailErr="detailErr" @closeBoard="showChooseBoard = false"
      @chooseFinish="chooseFinish" @clickSubmit="clickSubmit" v-if="showChooseBoard" />

    <!-- 地址选择 -->
    <PopupAddrList v-model="showLocationSelector" :logisticsServicesInfo="logisticsServicesInfo" :simpleAddMode="false"
      :isShowLogistics="true" @change="onPopupAddrChange" />
    <!-- 底部吸底按钮 -->
    <div class="fixed-bottom">
      <template v-if="isDataGet">
        <div class="tips-state" v-if="!onSaleState">
          该商品已下架，请选购其他商品!
        </div>
        <div class="tips-stock" v-if="onSaleState && !stockState">
          所选地区暂时无货，非常抱歉！
        </div>
        <div class="tips-stock" v-if="!userStatus">
          您暂无购买资格，非常抱歉！
        </div>
        <div class="tips-stock" v-if="!regionalSalesState">
          抱歉，此商品在所选区域暂不支持销售!
        </div>
        <div class="tips-stock" v-if="!limitState">
          该商品限购，请选购其他商品!
        </div>
      </template>
      <div class="cart" @click="toCart">
        <div class="img">
          <img src="./assets/icon-cart.png" alt="" />
          <div :class="{ num: true, animation: shakeAnim }" v-show="cartNum > 0"
            :style="'right:' + (cartNum > 99 ? '-8Px' : cartNum > 9 ? '-4Px' : '-2Px')">
            {{ cartNum }}
          </div>
        </div>
        <p>购物车</p>
      </div>

      <button class="btn1" @click="clickAddCart" :disabled="cartButtonDisabledStatus">
        加入购物车
      </button>
      <button class="btn2" @click="buyGoods" :disabled="cartButtonDisabledStatus">
        立即购买
      </button>
    </div>

    <GiftPopup v-model="giftPopupShow" :giftList="giftDetailsList" :goodsNum="goodsNum">
    </GiftPopup>

    <div class="to-up" ref="toUpRef" v-if="isShowToUp">
      <img @click="toUpClick" class="to-up-img" src="./assets/toUp.png" alt="">
    </div>
  </div>
</template>

<script>
import { GroupSend, log, setWeiXinShareData, share, urlAppend } from 'commonkit'
import qs from 'qs'
import Big from 'big.js'
import Cell from '@/components/Cell/index.vue'
import PopupAddrList from '@/components/Address/PopupAddrList.vue'
import Slider from '@/views/WoMall/goods/components/Slider.vue'
import Dropdown from '@/views/WoMall/goods/components/DropDown.vue'
import GoodsChooseBoard from '@/views/WoMall/goods/components/GoodsChoose.vue'
import GoodsIntroduce from '@/views/WoMall/goods/components/GoodsIntroduce.vue'
import GoodsParameter from '@/views/WoMall/goods/components/GoodsParam.vue'
import {
  checkSkuSale,
  getActiveList,
  getGiftDetails,
  getLimitAreaList,
  isWhiteUser,
  isWhiteUserLimitCheck, productIntroduction,
  queryPredictSkuPromise
} from '@/api/goods'
import { getBuyNowGoods } from '@/api/newCart'
import { jdAddressCheck } from '@/api/order'
import GoodsDetail, { removeSpecPrefix } from '@/utils/goodsDetail'
import { fenToYuan, priceComputeFromSku, splitAmt } from '@/utils/amount'
import { getDefaultShareUrl, shareData } from '@/utils/share'
import { buyProductNow, buyProductNowSession, curDeveloperId, getBizCode, getSupplierName } from '@/utils/curEnv'
import envConfig from '@/env.config'
import { isEmpty } from 'lodash'
import GiftPopup from '@/components/Goods/GiftPopup.vue'
import { setFrontCache } from '@/api/cached'

const cacheSkuGiftDetailsList = new Map()
export default {
  components: {
    GiftPopup,
    PopupAddrList,
    Dropdown,
    Slider,
    Cell,
    GoodsChooseBoard,
    GoodsIntroduce,
    GoodsParameter
  },
  data() {
    return {
      showMenus: true,
      skuList: null, // skuList。不进行 vue 代理
      listImageUrl: '', // 分享图片
      sliderItems: [], // 轮播图 图片
      goodsId: this.$route.params.id, // 商品id
      goodsParam: '', // 商品规格参数
      showChooseBoard: false, // 是否显示选择规格页面
      goodsNum: 1, // 购买的商品个数
      showDetail: false, // true显示的是商品详情页，false显示商品概览页
      detailTab: 'introduce',
      goodsDetailTab: 'introduce',
      showLocationSelector: false,
      detailImageUrl: '', // 配置的商品详情介绍的图片，非京东的商品商品详情显示这个内容
      loanProductList: [], // 借款产品列表（就是当前商品可以分多少期）
      topRightPop: false, // 右上角功能区是否显示
      shakeAnim: false, // 购物车添加成功抖动动画
      popType: '', // 弹窗类型 1.点击规格 2.购物车 3.立即购买
      skuNum: -1, // 支持购买sku数量
      // 下面是商品详情所有涉及的状态
      stockState: true, // 库存状态,true是有库存，false没有库存
      userStatus: true, // 是否为特殊商品的白名单用户（true：是，false：不是）
      // isForbidden: true, // 商品规格选择弹窗面板 默认可以根据商品情况选择规格
      regionalSalesState: true, // 所选区域暂不支持销售，true没有区域限制，false有区域限制
      limitState: true, // 白名单用户是否限购，true没有限购，false有限购
      marketTemplatesType1: [], // 活动营销位信息 type1
      marketTemplatesType4: [], // 活动营销位信息 type4
      // 金额
      reducePrice: 0, // 沃享赢减免金额
      // 新的规格计算
      goodsDetailData: {}, // 当前商品接口参数
      goods: null, // GoodsDetail 对象
      spu: null, // 当前商品信息
      currSku: {}, // 当前选中的 sku 商品，后台返回的源数据格式。不进行 vue 代理
      currSkuGift: [], // 当前 sku 赠品
      specsList: [], // 规格二维列表
      curSpecs: [], // 当前选中的规格
      curDisabledSpecs: [], // 当前禁止选中的规格
      isDataGet: false,
      detailErr: false, // 商品详情接口，是否为非 0000 故障
      giftPopupShow: false,
      giftDetailsList: [],
      showDetailKey: Math.random(),
      screenWidth: window.innerWidth,
      screenSize: '',
      logisticsServicesInfo: {
        logisticsType: 1,
        returnRuleStr: '',
        predictContent: '预计48小时之内发货',
        isJD: false
      },
      isJD: false,
      isShowLogisticsServices: false,
      isShowToUp: false,
      innerWidth: 0,
      innerHeight: 0,
      promotionList: [
        { title: '0元换新机', desc: '存保证金 购机付至0元', link: '' },
        { title: '0元换新机', desc: '存保证金 购机付至0元', link: '' }
      ]
    }
  },
  computed: {
    // 当前sku的 赠品数量
    currSkuGiftNum() {
      const { giftList } = this.currSku
      return giftList ? giftList.length : 0
    },
    // 规格组件的按钮状态控制
    goodsChooseBoardButtonDisabledStatus() {
      return this.onSaleState && this.stockState && this.userStatus && this.regionalSalesState && this.limitState
    },
    // 购物车按钮状态控制
    cartButtonDisabledStatus() {
      return this.isDataGet ? !this.onSaleState || !this.stockState || !this.userStatus || !this.regionalSalesState || !this.limitState : true
    },
    // 是否登录
    isLogin() {
      return this.$store.state.user.isLogin
    },
    // 当前用户地址
    addressInfo() {
      return this.$store.state.user.addressInfo
    },
    curAddrInfo() {
      return this.$store.getters['user/curAddressInfo']
    },
    // 购车的商品数量
    cartNum() {
      return this.$store.getters['newCart/countByGoods']
    },
    // 已选规格计算
    paramString() {
      if (this.curSpecs && this.curSpecs.length > 0) {
        return removeSpecPrefix(this.curSpecs) + ' , ' + this.goodsNum
      } else {
        return this.goodsNum
      }
    },
    // 地址回旋
    locationText() {
      const info = this.curAddrInfo
      return `${info.provinceName} ${info.cityName} ${info.countyName} ${info.townName || ''} `
    },
    // 计算售价价格
    promotionPrice() {
      const amt = priceComputeFromSku(this.currSku)[0]
      return splitAmt(amt)[0] === 'NaN' ? null : splitAmt(amt)
    },
    // 计算原始价格
    originalPrice() {
      const amt = priceComputeFromSku(this.currSku)[1]
      return amt ? fenToYuan(amt) : ''
    },
    // 预计到手价格
    finalPrice() {
      if (this.promotionPrice && this.promotionPrice[0] && this.reducePrice > 0) {
        const price = new Big(Number(this.promotionPrice[0] + '.' + this.promotionPrice[1]))
        const reduce = new Big(this.reducePrice).div(100)
        const showGetPrice = price.minus(reduce).toFixed(2)
        return Number(showGetPrice) > 0 ? showGetPrice : '0.00'
      } else {
        return '0.00'
      }
    },
    // 是否限购
    xgObj() {
      const limitTemplate = this.goodsDetailData.limitTemplate
      let limitText = ''
      if (limitTemplate && limitTemplate?.limitCountType) {
        switch (limitTemplate.limitCountType) {
          case '1':
            limitText = `每人每次限购${limitTemplate.limitNum}件`
            break
          case '2':
            limitText = `每人限购${limitTemplate.limitNum}件`
            break
          default:
            limitText = ''
        }
      }
      const limitNum = limitTemplate && limitTemplate?.limitNum ? limitTemplate.limitNum : 1
      return {
        isXg: this.goodsDetailData.isXg === '1',
        limitNum,
        limitText
      }
    },
    // 是否起购
    lowestBuyObj() {
      const lowestBuyValue = this.currSku?.lowestBuy
      const isLowestBuy = lowestBuyValue ? parseInt(lowestBuyValue, 10) > 1 : false
      const lowestBuyNum = lowestBuyValue ? parseInt(lowestBuyValue, 10) : 1
      const lowestBuyText = lowestBuyValue ? `${lowestBuyNum}件起购` : ''

      return {
        isLowestBuy,
        lowestBuyNum,
        lowestBuyText
      }
    },
    isShowActivity() {
      return (this.xgObj.isXg && this.xgObj.limitText) || this.lowestBuyObj.isLowestBuy || this.currSkuGiftNum
    },
    // 商品售卖状态
    onSaleState() {
      // eslint-disable-next-line eqeqeq
      // state： 0-不能购买，1-上架，2-下架，null-状态异常
      return !(this.currSku && this.currSku.state === '2')
    },
    isShowBizCode() {
      switch (getBizCode()) {
        case 'welfaresop':
        case 'labor':
        case 'ziying':
          return true
        default:
          return false
      }
    }
  },
  watch: {
    // 当前显示的sku
    currSku(newValue, oldValue) {
      // 根据当前商品信息更新轮播图
      if (!newValue.detailImageUrl) return
      if (typeof newValue.detailImageUrl === 'string') {
        this.sliderItems = newValue.detailImageUrl[0].split(',').map((item) => ({ imgUrl: item }))
      } else {
        this.sliderItems = newValue.detailImageUrl.map((item) => ({ imgUrl: item }))
      }

      // 重置当前分期选中项
      if (this.loanProductList && this.loanProductList.length > 0) {
        this.$store.commit('changeLoanProductCode', this.loanProductList[0].code)
      } else {
        this.$store.commit('changeLoanProductCode', '')
      }
      // 只要有sku就计算库存
      this.getStockState()
    },
    detailTab(newValue) { },
    isLogin: {
      handler(val) {
        if (val === null) {
          // 登录还没有结果，不处理
        } else if (typeof val === 'boolean') {
          // 有明确的登录状态了
          this.init()
        }
      },
      immediate: true
    },
    marketTemplatesType1() {
      this.getElectronic()
    }
  },
  methods: {
    getBizCode,
    goToPromotionDetail(item) {
      const { reqUrl } = item
      // 解析 reqUrl
      const urlObj = new URL(reqUrl)
      const currentUrl = window.location.href
      // 更新或添加参数
      urlObj.searchParams.set('goodsId', this.goodsId)
      urlObj.searchParams.set('skuId', this.currSku.skuId)
      urlObj.searchParams.set('callback', encodeURIComponent(currentUrl))

      // 获取完整URL
      const url = urlObj.toString()
      // 判断当前商品是否有货
      if (!this.stockState) {
        this.$toast('抱歉，所选商品暂时无货，请选择其他商品办理。')
      } else {
        try {
          window.location.href = url
        } catch (error) {
          console.error('跳转保证金页面失败:', error)
          this.$toast('跳转失败，请稍后再试')
        }
      }
    },
    determineScreenSize() {
      let size
      if (this.screenWidth < 320) {
        size = 'type1'
      } else if (this.screenWidth >= 320 && this.screenWidth <= 540) {
        size = 'type2'
      } else {
        size = 'type3'
      }
      this.screenSize = size
    },
    handleResize() {
      this.screenWidth = window.innerWidth
      this.determineScreenSize()
      this.$nextTick(() => {
        this.showDetailKey = Math.random() // 手动触发重新渲染
        this.$set(this.currSku, 'showDetailKey', this.showDetailKey)
      })
    },
    showGiftDetailList() {
      this.giftPopupShow = true
      // 不是随便触发的
      this.getGiftsDetails()
    },
    async getGiftsDetails() {
      const gs = new GroupSend()
      const tempGiftDetailsList = []
      this.giftDetailsList = []

      const { skuId, supplierCode } = this.currSku
      if (cacheSkuGiftDetailsList.has(skuId)) {
        this.giftDetailsList = cacheSkuGiftDetailsList.get(skuId)
        return
      }
      if (this.giftDetailsList.length <= 0) {
        this.currSkuGift.forEach((item, index) => {
          gs.push(getGiftDetails({ supplierSkuId: item.giftId, supplierCode: supplierCode || 'jd_yg' })).then(([err, json]) => {
            err ? tempGiftDetailsList.splice(index, 0, {}) : tempGiftDetailsList.splice(index, 0, { ...json, giftType: item.giftType, giftNum: item.giftNum, belongToSkuMaxNum: item.belongToSkuMaxNum, belongToSkuMinNum: item.belongToSkuMinNum })
            // 做到实时的更新
            this.giftDetailsList = tempGiftDetailsList
          })
        })
        gs.onComplete = () => {
          // this.$toast.queueClear()
          cacheSkuGiftDetailsList.set(skuId, this.giftDetailsList)
        }
      }
    },
    getSupplierCodeToName(supplierCode) {
      const supplierName = getSupplierName(supplierCode)
      if (supplierName === '其他') {
        return ''
      }
      return supplierName
    },
    async addressCheck() {
      this.$toast.queueLoading()
      const [err, json] = await jdAddressCheck()
      this.$toast.queueClear()
      if (err) {
        this.$toast(err.msg)
        return false
      }
      if (!json) {
        this.$alert({
          titleStyle: {
            fontSize: this.screenSize !== 'type3' ? '16px' : '22Px',
            color: '#171E24'
          },
          content: ('由于物流配送地址库规则升级，收货地址需要精确到街道，请您重新设置地址，按指引操作及保存!'),
          contentStyle: {
            marginTop: '19px',
            marginBottom: '25px',
            textAlign: 'center',
            fontSize: this.screenSize !== 'type3' ? '16px' : '18Px',
            color: '#5A6066'
          },
          confirmTxt: '修改地址',
          confirmStyle: {
            fontSize: this.screenSize !== 'type3' ? '16px' : '18Px',
            color: '#007EE6'
          },
          confirmCb: () => {
            this.$router.push({ name: 'address-edit', query: { addrId: this.curAddrInfo.addressId, isInvalid: '1' } })
          },
          cancelTxt: '取消',
          cancelStyle: {
            fontSize: this.screenSize !== 'type3' ? '16px' : '18Px',
            color: '#007EE6'
          },
          cancelCb: () => { }
        })
        return false
      }
      return true
    },
    async onPopupAddrChange() {
      // 福利汇获取url的发展人id
      if (getBizCode() === 'fulihui') {
        this.getDeveloperId()
      }
      await this.$store.dispatch('user/queryDefaultAddr')
      if (this.isLogin) {
        this.$store.dispatch('newCart/query')
      }
      this.$toast.queueLoading()
      await this.loadGoodsDetail() // 查询当前商品的信息
      if (this.isJD) {
        await this.queryPredictSku()
        await this.checkIsSkuSale()
      }
      this.isShowLogisticsServices = getBizCode() !== 'fupin'

      if (!this.detailErr) {
        // 没有 goodsDetail 接口错误，继续执行，否则不要执行了（执行了的话，会导致 queueClear 清除了 goodsDetail 的错误提示）
        this.getStockState()
        if (this.isLogin) {
          await this.addressCheck()
        }
        this.$toast.queueClear()
      }
    },
    async queryPredictSku() {
      const info = this.curAddrInfo
      const addressInfo = JSON.stringify({
        provinceId: info.provinceId,
        provinceName: info.provinceName,
        cityId: info.cityId,
        cityName: info.cityName,
        countyId: info.countyId,
        countyName: info.countyName,
        townId: info.townId,
        townName: info.townName
      })
      const params = {
        supplierCode: this.currSku.supplierCode,
        supplierSkuId: this.currSku.supplierSkuId,
        skuNum: this.goodsNum,
        addressInfoStr: addressInfo
      }
      const [err, res] = await queryPredictSkuPromise(params)
      if (!err) {
        this.logisticsServicesInfo = {
          ...this.logisticsServicesInfo,
          ...res
        }
        return
      }
      this.logisticsServicesInfo = {
        ...this.logisticsServicesInfo,
        predictContent: '预计48小时之内发货'
      }
    },
    async checkIsSkuSale() {
      const params = {
        supplierCode: this.currSku.supplierCode,
        supplierSkuId: this.currSku.supplierSkuId
      }
      const [err, res] = await checkSkuSale(params)
      if (!err) {
        this.logisticsServicesInfo = {
          ...this.logisticsServicesInfo,
          ...res[0]
        }
        return
      }
      this.logisticsServicesInfo = {
        ...this.logisticsServicesInfo,
        logisticsType: 0,
        returnRuleStr: ''
      }
    },
    // 获取电子券getElectronic
    async() {
      if (this.isLogin && this.marketTemplatesType1 && this.marketTemplatesType1.length > 0) {
        if (
          this.marketTemplatesType1?.[0] &&
          this.marketTemplatesType1[0].reqType === '1' &&
          this.marketTemplatesType1[0].templateNo === 'wxy618'
        ) {
          const [err, json] = await getActiveList({ templateNo: this.marketTemplatesType1[0].templateNo || '' })
          if (!err) {
            this.reducePrice = json
          }
        }
      }
    },
    marketingBtn() {
      const { reqType, reqUrl, templateNo } = this.marketTemplatesType1[0]
      // reqType=1 跳转链接形式
      if (reqType === '1') {
        if (templateNo === 'wxy618') {
          // 保证金活动，特殊处理，拼接 goodsId,skuId,callback
          const host = window.location.origin
          const path = envConfig.VUE_APP_BASE_URL
          const callbackUrl = host + path + `/goodsdetail/${this.goodsId}/${this.currSku.skuId}?distri_biz_code=ziying`
          if (this.reducePrice > 0 && Number(this.finalPrice) === 0) {
            this.$toast('您已经参与过该活动，请下次再试吧')
            return
          }
          window.location.href = urlAppend(reqUrl, {
            goodsId: this.goodsId,
            skuId: this.currSku.skuId,
            callback: callbackUrl
          })
        } else {
          window.location.href = reqUrl
        }
      }
    },
    // 规格变动重新获取一下stockState状态 检查商品库存
    getStockState() {
      this.stockState = !!(this.currSku.stock && Number(this.currSku.stock) > 0)
    },
    // 分享初始化
    async shareInit() {
      const bizCode = getBizCode()
      const intro = () => {
        const sku = this.skuList[0]
        if (bizCode === 'ziying') {
          return sku.comment || '足不出户囤遍好物！购商品，来精选。'
        } else if (bizCode === 'fupin') {
          return sku.comment || '消费帮扶，共献爱心，乡村振兴，有我联通。'
        } else if (bizCode === 'fulihui') {
          return sku.comment || '足不出户囤遍好物！购商品，来福利汇。'
        } else if (bizCode === 'lnzx') {
          return sku.comment || '联农智选，好货甄选，品质可信。'
        } else {
          return sku.comment || sku.name || sku.merchantName || ''
        }
      }
      shareData.title = this.skuList[0].name
      shareData.describe = intro()
      shareData.picUrl = this.listImageUrl ? this.listImageUrl : ''
      shareData.link = await getDefaultShareUrl()
      log('[GOODS-DETAIL] shareInfo', shareData)
      setWeiXinShareData(shareData)
    },
    // 点击右上角点点点的分享
    onDropdownShare(e) {
      // 分享功能，不能写在异步函数中
      share(shareData, e)
    },
    // 外部列表已选规格点击事件
    chooseSpecification() {
      this.popType = '1'
      this.showChooseBoard = true
    },
    setNewRoute(goodsId, skuId) {
      const host = window.location.origin
      const path = envConfig.VUE_APP_BASE_URL
      const urlSearch = window.location.search
      const urlQueryStr = urlSearch ? urlSearch.split('?')[1] : ''
      const urlQueryObj = qs.parse(urlQueryStr)

      const callbackUrl = host + path + `/goodsdetail/${goodsId}/${skuId}` + urlSearch
      history.replaceState(null, '', callbackUrl)
      // 将原来的 this.$route 克隆一份
      const _routeOld = this.$route
      const _routeNew = Object.assign({}, _routeOld)
      // !这个地方是BUG，无法更新，更新就报错
      // !可以更新path，但是没办法更新fullPath，更新就是无感知刷新两次（接口层面可以看出来），目前是无解的
      // _routeNew.fullPath = callbackUrl
      _routeNew.path = `/goodsdetail/${goodsId}/${skuId}`
      // 更新 参数
      _routeNew.params = {
        id: goodsId,
        skuId: skuId
      }
      _routeNew.query = {
        ..._routeOld.query,
        ...urlQueryObj
      }
      // 调用 router.history 里的更新方法 cb 传入最新的route就可以了
      this.$router.history.cb(_routeNew)
    },
    // 默认以及手动规格选择
    async chooseFinish(spec, num) {
      // 设置新的规格，当前的
      if (spec) {
        this.goods.setSpecs(spec)
      }
      // 保存之前的sku
      const prevSku = this.currSku
      // 查询最新的sku
      this.currSku = this.goods.querySku()
      this.isJD = this.currSku.supplierCode.indexOf('jd_') > -1

      if (this.isJD && spec) {
        // 这个地方开始 获取新的商品详情介绍
        this.getProductIntroduction()
      }

      this.$set(this.logisticsServicesInfo, 'isJD', this.isJD)
      this.isShowLogisticsServices = getBizCode() !== 'fupin'
      this.$set(this.currSku, 'isSpecsComplete', this.goods.isSpecsComplete())
      // 获取最新 sku 的赠品
      this.currSkuGift = this.currSku?.giftList || []
      // 查询最新可选规格
      this.curSpecs = this.goods.queryCurSpecs()
      // 查询最新不可选规格
      this.curDisabledSpecs = this.goods.queryDisabledSpecs()
      this.goodsNum = num
      this.setNewRoute(this.goodsId, this.currSku.skuId)
      if (prevSku.skuId && prevSku.skuId !== this.currSku.skuId) {
        // 更换sku，查库存
        this.getStockState()
      }
      if (spec && this.isJD) {
        await this.queryPredictSku()
        await this.checkIsSkuSale()
      }
    },
    // 规格选择弹窗点击确认及购物按钮
    clickSubmit(type, num) {
      // type弹窗内的 0:确定按钮  1：加入购物车  2：立即购买
      if (!this.goods.isSpecsComplete()) {
        this.$toast('请选择完整商品规格！')
        return
      } else {
        this.showChooseBoard = false
      }
      this.goodsNum = num
      switch (type) {
        case 0:
          if (this.popType === '2') {
            // 加入购物车
            this.addCart()
          } else {
            // 立即购买
            this.sumitHandle()
          }
          return
        case 1:
          if (Number(this.skuNum) >= 0) this.addCart()
          return
        case 2:
          if (Number(this.skuNum) >= 0) this.sumitHandle()
          break
        default:
      }
    },
    chooseAddress() {
      this.showLocationSelector = true
    },
    toCart() {
      this.$router.push('/cart')
    },
    // 点击加入购物车按钮
    clickAddCart() {
      if (!this.isLogin) {
        this.$store.dispatch('user/login')
        return
      }
      if (Number(this.skuNum) === 1) {
        if (!this.goods.isSpecsComplete()) {
          this.$toast('请选择完整商品规格！')
          return
        }
        this.addCart()
      } else {
        this.popType = '2'
        this.showChooseBoard = true
        if (!this.goods.isSpecsComplete()) {
          this.$toast('请选择完整商品规格！')
        }
      }
    },
    // 加入购物车
    async addCart() {
      const isPassed = await this.addressCheck()
      if (!isPassed) {
        return
      }
      if (!this.goods.isSpecsComplete()) {
        this.$toast('请选择完整商品规格！')
        return
      }
      // 需要判断商品状态 state： 0-不能购买，1-上架，2-下架，null-状态异常
      if (this.currSku.state !== '1') {
        this.$toast('商品已下架，看看其他商品吧')
        return
      }
      this.shakeAnim = false
      if (!this.stockState) {
        this.$toast('商品暂时无货，看看其他商品吧')
        return
      }
      const info = this.curAddrInfo
      const addressInfo = JSON.stringify({
        provinceId: info.provinceId,
        provinceName: info.provinceName,
        cityId: info.cityId,
        cityName: info.cityName,
        countyId: info.countyId,
        countyName: info.countyName,
        townId: info.townId,
        townName: info.townName
      })
      this.$toast.queueLoading()
      const err = await this.$store.dispatch('newCart/add', {
        goodsId: this.goodsId,
        skuId: this.currSku.skuId,
        goodsNum: this.goodsNum,
        addressInfo
      })
      this.$toast.queueClear()
      if (err) {
        // 添加购物车失败
        this.$toast(err.msg)
      } else {
        // 添加购物车成功
        setTimeout(() => {
          this.shakeAnim = true
          this.$toast('加入购物车成功')
          if (this.lowestBuyObj.isLowestBuy) {
            this.goodsNum = +this.lowestBuyObj.lowestBuyNum
          } else {
            this.goodsNum = 1
          }
        }, 0)
      }
    },
    async loadGoodsDetail() {
      // 初始化规格计算对象
      // !TODO:这个地方要给出  1003  状态码的处理
      this.goods = new GoodsDetail(this.goodsId, this.$route.params.skuId)
      // 获取当前的商品的spu
      const json = await this.goods.querySpu()
      console.warn('任何情况下都后端都会返回数据日志', json)
      const goodsDetail = json.data

      if (json.code !== '0000') {
        if (json.code === '8888') {
          this.$toast('此商品信息更新中，暂时无法购买，请您选购其他商品。')
        } else {
          this.$toast(json.msg)
        }
        this.detailErr = true
      }

      if (isEmpty(goodsDetail)) return

      // 查询最新sku
      this.currSku = this.goods.querySku()
      this.isJD = this.currSku.supplierCode.indexOf('jd_') > -1

      if (this.isJD) {
        // 这个地方开始 获取新的商品详情介绍
        this.getProductIntroduction()
      }

      this.$set(this.logisticsServicesInfo, 'isJD', this.isJD)
      this.$set(this.currSku, 'isSpecsComplete', this.goods.isSpecsComplete())
      // 获取最新 sku 的赠品
      this.currSkuGift = this.currSku?.giftList || []
      // 查询所有规格
      this.specsList = this.goods.querySpecsList()
      this.specsList = this.specsList.filter(item => item.length > 0)
      // 查询最新可选规格
      this.curSpecs = this.goods.queryCurSpecs()
      // 查询当前不可选规格
      this.curDisabledSpecs = this.goods.queryDisabledSpecs()
      // 获取当前可用sku数量
      this.skuNum = this.goods.querySkuCount()
      this.setNewRoute(this.goodsId, this.currSku.skuId)
      if (goodsDetail) {
        this.goodsDetailData = goodsDetail
        // 设置商品参数信息
        this.goodsParam = goodsDetail.skuList[0].paramDetail
        // 判断是否有最低起购
        if (this.lowestBuyObj.isLowestBuy) {
          this.goodsNum = this.lowestBuyObj.lowestBuyNum
        }
        // 设置商品规格信息
        this.skuList = Object.freeze(goodsDetail.skuList)
        // 分享时的图片
        this.listImageUrl = goodsDetail.listImageUrl
        // 活动营销位信息 （templateType 为 营销位类型）
        this.marketTemplatesType1 = goodsDetail.marketTemplates?.filter(
          (item) => {
            return item.templateType === '1'
          }
        )
        this.marketTemplatesType4 = goodsDetail.marketTemplates?.filter(
          (item) => {
            return item.templateType === '4'
          }
        )
        // 激活客户端分享
        this.shareInit()
        // 获取活动营销模版号
        // 如果isCheckWhiteUser存在且=1 需要调白名单接口
        if (goodsDetail.isCheckWhiteUser && goodsDetail.isCheckWhiteUser === '1') {
          // 如果登录的话，查询用户是否有资格在白名单内
          if (this.isLogin) {
            const [err, json] = await isWhiteUserLimitCheck(this.goodsId)
            if (!err) {
              this.limitState = json
            }
          }
        }
        this.querySale() // 查询商品限制销售区域
      }
    },
    async getProductIntroduction() {
      const { supplierSkuId, supplierCode } = this.currSku
      const [err, json] = await productIntroduction({
        supplierSkuId,
        supplierCode
      })
      if (!err) {
        this.$set(this.currSku, 'introduction', json)
      }
    },
    // 点击立即购买弹起弹窗
    buyGoods() {
      if (!this.isLogin) {
        this.$store.dispatch('user/login')
        return
      }
      if (Number(this.skuNum) === 1) {
        if (!this.goods.isSpecsComplete()) {
          this.$toast('请选择完整商品规格！')
          return
        }
        this.sumitHandle()
      } else {
        this.popType = '3'
        this.showChooseBoard = true
        if (!this.goods.isSpecsComplete()) {
          this.$toast('请选择完整商品规格！')
        }
      }
    },
    // 去购买
    async sumitHandle() {
      const isPassed = await this.addressCheck()
      if (!isPassed) {
        return
      }
      if (!this.goods.isSpecsComplete()) {
        this.$toast('请选择完整商品规格！')
        return
      }
      if (!this.stockState) {
        this.$toast('商品暂时无货，看看其他商品吧')
        return
      }
      if (!this.isLogin) {
        this.$store.dispatch('user/login')
        return
      }
      // 需要判断商品状态 state： 0-不能购买，1-上架，2-下架，null-状态异常
      if (this.currSku.state !== '1') {
        this.$toast('商品已下架，看看其他商品吧')
        return
      }
      // 这个地方需要校验
      const info = this.curAddrInfo
      const addressInfo = JSON.stringify({
        provinceId: info.provinceId,
        provinceName: info.provinceName,
        cityId: info.cityId,
        cityName: info.cityName,
        countyId: info.countyId,
        countyName: info.countyName,
        townId: info.townId,
        townName: info.townName
      })
      this.$toast.queueLoading()
      const [res] = await getBuyNowGoods({
        goodsId: this.goodsId,
        skuId: this.currSku.skuId,
        goodsNum: this.goodsNum,
        addressInfo,
        bizCode: getBizCode('ORDER')
      })
      this.$toast.queueClear()
      if (res.code === '1003') {
        this.$alert({
          titleStyle: {
            fontSize: this.screenSize !== 'type3' ? '16px' : '22Px',
            color: '#171E24'
          },
          content: ('由于物流配送地址库规则升级，收货地址需要精确到街道，请您重新设置地址，按指引操作及保存!'),
          contentStyle: {
            marginTop: '19px',
            marginBottom: '25px',
            textAlign: 'center',
            fontSize: this.screenSize !== 'type3' ? '16px' : '18Px',
            color: '#5A6066'
          },
          confirmTxt: '修改地址',
          confirmStyle: {
            fontSize: this.screenSize !== 'type3' ? '16px' : '18Px',
            color: '#007EE6'
          },
          confirmCb: () => {
            this.$router.push({ name: 'address-edit', query: { addrId: info.addressId, isInvalid: '1' } })
          },
          cancelTxt: '取消',
          cancelStyle: {
            fontSize: this.screenSize !== 'type3' ? '16px' : '18Px',
            color: '#007EE6'
          },
          cancelCb: () => { }
        })
      } else if (res.code === '0000') {
        // 缓存立即购买的数据
        buyProductNow.set(res)
        buyProductNowSession.set(res)
        // 调用setFrontCache接口缓存数据
        this.$toast.queueLoading()
        await setFrontCache({
          content: JSON.stringify(res)
        })
        this.$toast.queueClear()
        // 直接下单，给订单页传 goodsId  skuId   goodsNum 用于订单页回显数据
        // 检查当前页面是否有curSelectedMoney、curSelectedTime和orderNo参数
        const query = {
          goodsId: this.goodsId,
          skuId: this.currSku.skuId,
          goodsNum: this.goodsNum,
          supplierCode: this.currSku.supplierCode
        }

        // 如果当前页面URL中包含这三个参数，则添加到query对象中
        if (this.$route.query.curSelectedMoney) {
          query.curSelectedMoney = this.$route.query.curSelectedMoney
        }
        if (this.$route.query.curSelectedTime) {
          query.curSelectedTime = this.$route.query.curSelectedTime
        }
        if (this.$route.query.orderNo) {
          query.orderNo = this.$route.query.orderNo
        }

        this.$router.push({
          path: '/orderconfirm',
          query
        })

        if (this.lowestBuyObj.isLowestBuy) {
          this.goodsNum = +this.lowestBuyObj.lowestBuyNum
        } else {
          this.goodsNum = 1
        }
      } else {
        this.$toast(res.msg)
      }
    },
    // 福利汇商城 获取url的发展人id
    getDeveloperId() {
      const developerId = curDeveloperId.get()
      if (!developerId && this.$route.query.developerId) {
        // 通过触点分享的链接进入，保存触点的发展人id
        curDeveloperId.set(this.$route.query.developerId)
      }
    },
    scrollHandler(event) {
      // this.scrollY = -y
      // const scroll = this.$refs.scroll.scroll
      // const maxScrollY = scroll.maxScrollY // 是个负值
      const scrollTop = event.target.scrollTop
      const scrollY = scrollTop
      const tabOpacity = scrollTop / (document.body.clientWidth / 2.0) // 透明度
      this.$refs.detailTabHeader.style.opacity = tabOpacity // 滚动到轮播半屏opacity:0~1
      const goodsY = this.$refs.goodsIntro.offsetHeight - 34
      if (!this.showDetail) {
        this.showDetail = true
      }
      if (scrollTop <= 100) {
        this.isShowToUp = false
      } else {
        this.isShowToUp = true
      }
      if (scrollY < goodsY) {
        // 偏移量小于商品详情
        this.showMenus = true
        this.goodsDetailTab = 'introduce'
      } else {
        // 偏移量大于商品详情
        this.showMenus = false
        this.goodsDetailTab = 'description'
      }
    },
    toUpClick() {
      this.$refs.scrollWrapper.scrollTop = 0
    },
    setGoodsDetailTab(tab) {
      // tab: introduce / description
      this.goodsDetailTab = tab
      if (tab === 'introduce') {
        this.$refs.scrollWrapper.scrollTo(0, -0, 100)
      } else {
        this.$refs.scrollWrapper.scrollTo(0, this.$refs.goodsIntro.offsetHeight - 34, 100)
      }
    },
    async init() {
      if (this.$route.query.from === 'licai') {
        // 查询登录状态，此处不用再次检查了，因为App.vue入口已经检查过了
        const [err, json] = await isWhiteUser({ type: 1 })
        if (!err) {
          this.userStatus = json === false // false: 非特殊商品的白名单用户，true：特殊商品的白名单用户，其余按白名单用户处理
          // this.isForbidden = whiteUser.data === false // 特殊商品的白名单用户可以选商品规格，非白名单用户不能选商品规格
        }
      }
      await this.onPopupAddrChange()
      await this.getElectronic()
    },
    async querySale() {
      const info = this.curAddrInfo
      const params = {
        area: JSON.stringify({
          provinceId: info.provinceId,
          cityId: info.cityId,
          countyId: info.countyId,
          townId: info.townId
        }),
        goodsIdList: this.goodsId
      }
      // 商品数据正常时才进行区域限购查询
      if (!this.detailErr) {
        const [err, json] = await getLimitAreaList(params)
        if (!err && json) {
          this.regionalSalesState = json.length <= 0
        }
        this.isDataGet = true
      } else {
        this.isDataGet = false
      }
    }
  },
  created() {
    this.$store.dispatch('user/queryLoginStatus')
    this.determineScreenSize()
    window.addEventListener('resize', this.handleResize)
  },
  mounted() {
    this.$nextTick(() => {
      this.innerWidth = window.innerWidth
      this.innerHeight = window.innerHeight
    })
  },
  destroyed() {
    this.$toast.clear()
    window.removeEventListener('resize', this.handleResize)
  }
}
</script>

<style scoped lang="less">
@screen-small: 320px;
@screen-medium: 540px;
@fixed-bottom-height: 49Px;

.goods-detail {
  position: relative;
  height: 100vh;

  .to-up {
    position: fixed;
    z-index: 999;
    bottom: 200px;
    right: 20px;

    .to-up-img {
      width: 76px;
      height: 76px;
    }
  }
}

.scroll-list-wrap {
  width: 100vw;
  height: calc(100vh - @fixed-bottom-height);
  overflow-y: scroll;
  scroll-behavior: smooth;
}

.slider-wrapper {
  position: relative;

  @media (max-width: @screen-small) {
    //width: 100vw;
    //height: 100vw;
  }

  // 标准屏设备
  @media (min-width: @screen-small + 1) and (max-width: @screen-medium) {
    //width: 100vw;
    //height: 100vw;
  }

  // 大屏设备
  @media (min-width: @screen-medium + 1) {}
}

.slider-wrapper.goods__sold-out::before {
  background-size: 35%;
}

.goods-desc {
  width: 100%;
  margin-top: 30px;
  padding: 0 34px;
  background: #fff;

  @media (min-width: @screen-medium + 1) {
    margin-top: 15Px;
  }
}

.goods-title {
  font-family: PingFangSC-Medium;
  font-size: 32px;
  line-height: 45px;
  color: #171E24;

  @media (min-width: @screen-medium + 1) {
    font-size: 18Px;
    line-height: 1.5;
  }
}

.mall-icon {
  height: 100%;

  &:before {
    content: '沃百富';
    margin-right: 6px;
    padding: 4px 10px;
    font-size: 18px;
    color: #fff;
    background-color: #ff8a2d;
    vertical-align: middle;
    border-radius: 10px;
    line-height: 1;

    @media (min-width: @screen-medium + 1) {
      margin-right: 5Px;
      height: 100%;
      width: 50Px;
      //padding: 4Px 10Px;
      vertical-align: middle;
      font-size: 12Px;
      color: #fff;
      background-color: #ff8a2d;
      border-radius: 10px;
    }
  }
}

.goods-name {
  text-align: left;
  flex: 1;
}

.goods-suppliername {
  margin-right: 5px;
}

.goods-price-sales {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  margin-bottom: 34px;

  @media (min-width: @screen-medium + 1) {
    margin-top: 5Px;
    margin-bottom: 5Px;
  }

  .goods-price {
    display: flex;
    align-items: flex-end;
    font-size: 15Px;
    color: #171E24;

    @media (min-width: @screen-medium + 1) {
      margin-top: 10Px;
      margin-bottom: 10Px;
      font-size: 20Px;
    }
  }

  .product-sales {
    font-size: 14Px;
    color: #999999;
    text-align: center;
    line-height: 14Px;
    font-weight: 400;
  }
}

.goods-price .curr-price {
  font-size: 14Px;
}

.goods-price .curr-price i {
  font-size: 18Px;

  @media (min-width: @screen-medium + 1) {
    font-size: 24Px;
  }
}

.goods-price .del-price {
  position: relative;
  margin-left: 10Px;

  @media (min-width: @screen-medium + 1) {
    font-size: 14Px;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 46%;
    left: -2%;
    width: 110%;
    height: 1Px;
    background: #879099;
  }
}

.goods-price-reduce {
  background: #FFF2EB;
  border-radius: 12px;
  font-size: 13Px;
  color: #FF5741;
  font-weight: 500;
  padding: 6Px 13Px;
  margin-left: 20Px;

  @media (min-width: @screen-medium + 1) {
    font-size: 27Px;
  }
}

.devide-blank {
  width: 100%;
  height: 20px;
  background: rgba(247, 247, 247, 0.80);

  @media (min-width: @screen-medium + 1) {
    height: 10Px;
  }
}

.cell-wrapper {
  line-height: 1.5;
  margin-left: 34px;
  padding: 41px 34px 41px 0;

  @media (min-width: @screen-medium + 1) {
    margin-left: 0;
    padding: 15Px 40Px;
  }

  .delivery-logistics-info {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-left: 90px;
    margin-top: 10Px;
    font-size: 26px;

    @media (min-width: @screen-medium + 1) {
      margin-left: 75Px;
      font-size: 16Px;
    }

    .logistics-icon {
      margin-right: 10px;
      height: 25px;

      @media (min-width: @screen-medium + 1) {
        margin-right: 10Px;
        height: 15Px;
        font-size: 16Px;
      }
    }

    .logistics-info {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis; //文本溢出显示省略号
      white-space: nowrap; //文本不会换行
      font-size: 28px;

      @media (min-width: @screen-medium + 1) {
        font-size: 16Px;
        line-height: 1.5;
      }
    }
  }
}

.cell-wrapper.cell-wrapper-limit {
  @media (min-width: @screen-medium + 1) {
    margin-left: 0;
    padding: 15Px 40Px;
  }
}

.cell-wrapper.borderd {
  border-bottom: 1px solid rgba(229, 229, 229, 0.64);
}

.cell-wrapper .red {
  color: #FF4B33;
}

.cell-wrapper .cell {
  // height: 30px
}

.cell-wrapper /deep/ .cell-left {
  display: flex;
  justify-content: space-between;
  //width: 100%;
  padding-right: .3rem;
  overflow: hidden;
}

.cell-wrapper /deep/ .title {
  flex: none;
}

.cell-wrapper .cell-desc {
  flex: auto;
  font-size: 28px;
  line-height: 1.5;
  color: #171E24;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

  @media (min-width: @screen-medium + 1) {
    font-size: 16Px;
    line-height: 1.5;
  }
}

.cell-wrapper .cell-desc-all {
  flex: auto;
  font-size: 28px;
  line-height: 1.5;
  //width: 450px;
  color: #171E24;
  overflow: hidden;
  cursor: pointer;

  @media (min-width: @screen-medium + 1) {
    font-size: 16Px;
    line-height: 1;
  }
}

.cell-wrapper .cell-desc-all.cell-desc-limit {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-right: 10Px;
}

.cell-wrapper .cell-desc-all .limit-icon {
  margin-right: 5Px;
  padding: 4px 10Px;
  border-radius: 6Px;
  font-size: 12Px;
  border: 1Px solid #F23030;
  color: #F23030;
}

.cell-wrapper .cell-desc-all .limit-text {
  margin-right: 5Px;
  font-size: 24px;
  color: #F23030;

  @media (min-width: @screen-medium + 1) {
    font-size: 16Px;
    line-height: 1;
  }
}

.cell-wrapper .cell-desc-all .line {
  display: block;
  margin-bottom: 10px;

  @media (min-width: @screen-medium + 1) {
    margin-bottom: 0;
  }
}

.promotion-activity {
  background: linear-gradient(90deg, #FF6362 0%, #FFBE7A 100%);
  padding: 10Px;
}

.promotion-activity-title {
  color: #FFFFFF;
  font-size: 16Px;
  font-weight: 500;
  margin-bottom: 5Px;
}

.promotion-activity-content {
  border-radius: 8Px;
  padding: 13Px;
  background: linear-gradient(90deg, #FFEEEB 0%, #FFFAF9 100%);

  .promotion-item {
    width: 100%;
    min-height: 60px;
    display: flex;
    align-items: center;
    margin-bottom: 20Px;
    // padding: 20Px 0;
    //border-bottom: 1Px solid rgba(229, 229, 229, 0.64);
  }

  .promotion-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }

  .promotion-heart-img {
    width: 35px;
    height: 30px;
    display: block;
    vertical-align: middle;
    margin-right: 10px;
  }

  .promotion-content {
    flex: 1;
    font-size: 28px;
    color: #171E24;
    line-height: 1;

    // font-weight: 500;
    @media (min-width: @screen-medium + 1) {
      font-size: 16Px;
    }
  }

  .promotion-arrow {
    display: flex;
    align-items: center;
  }

  .promotion-desc {
    font-size: 28px;
    color: #FF780A;
    margin-right: 5Px;
    line-height: 1;

    @media (min-width: @screen-medium + 1) {
      font-size: 16Px;
    }
  }

  .arrow-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-top: 2px solid #FF780A;
    border-right: 2px solid #FF780A;
    transform: rotate(45deg);

    @media (min-width: @screen-medium + 1) {
      width: 20Px;
      height: 20Px;
    }
  }
}

.goods-intro {
  width: 100%;
  padding: 0 34px;
}

.goods-intro img {
  display: block;
  width: 100%;
}

.goods-intro p {
  margin-top: 42px;
  font-size: 26px;
  color: #879099;
  line-height: 38px;
}

.pullup-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100px;
  line-height: 100px;
  text-align: center;
  font-size: 30px;
  color: #171E24;
}

.pullup-box .icon {
  display: block;
  width: 47px;
  height: 47px;
  background: url(./assets/<EMAIL>) no-repeat center center;
  background-size: 22px;
}

.fixed-bottom {
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 9;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12Px;
  width: 100%;
  height: @fixed-bottom-height;
  color: #FFFFFF;
  background: #fff;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -50%;
    width: 200%;
    height: 1Px;
    background-color: #E2E8EE;
    transform: scale(.5)
  }

  .tips-stock {
    position: absolute;
    top: -37Px;
    left: 0;
    width: 100%;
    height: 38Px;
    line-height: 38Px;
    background: #FFF5DA;
    color: #BB4D00;
    font-size: 14Px;
    text-align: center;
  }

  .tips-state {
    position: absolute;
    top: -37Px;
    left: 0;
    width: 100%;
    height: 38Px;
    line-height: 38Px;
    background: #666666;
    color: #fff;
    font-size: 13Px;
    text-align: center;
  }

  .cart {
    flex: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 15Px;

    .img {
      position: relative;
      margin-top: 4Px;
      width: 23Px;

      img {
        display: block;
        width: 100%;
      }

      .num {
        position: absolute;
        top: 0;
        right: -2Px;
        display: flex;
        align-items: center;
        justify-content: center;

        min-width: 20Px;
        padding: 1Px 3Px;
        border: 1px solid #ff780a;
        border-radius: 9Px;
        background: #fff;
        font-size: 12Px;
        color: #FF780A;
        transform: translate(30%, -35%);
        transform-origin: center center;

        &.animation {
          animation: shake .15s linear 0s 2 alternate;
        }
      }

      @keyframes shake {
        from {
          transform: translate(40%, -35%) rotate(25deg);
        }

        to {
          transform: translate(20%, -35%) rotate(-25deg);
        }
      }
    }

    p {
      color: #171E24;
      font-size: 12Px;
      white-space: nowrap;
      transform: scale(.75);
      margin-top: 4Px;
    }
  }

  button {
    white-space: nowrap;
    outline: none;
    border: none;
    height: 36Px;
    width: 32%;
    font-size: 14Px;
    color: #fff;
    border-radius: 18Px;
  }

  .btn1 {
    margin-left: 12Px;
    margin-right: 12Px;
    background-image: linear-gradient(153deg, #FFD72D 0%, #FFAD1B 95%);

    &[disabled] {
      opacity: 0.5;
    }
  }

  .btn2 {
    background-image: linear-gradient(132deg, #FFA033 0%, #FF6D33 100%);

    &[disabled] {
      opacity: 0.5;
    }
  }
}

.fixed-bottom {
  margin-bottom: var(--saib);
}

.sticky-goods-header {
  width: 100%;
  height: 68px;
  display: flex;
  position: absolute;
  z-index: 99;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  box-shadow: 0 1px 0 0 #E5E5E5;
  opacity: 0;

  @media (min-width: @screen-medium + 1) {
    height: 45Px;
  }

  &:before,
  &:after {
    content: '';
    display: block;
  }
}

//.goods-detail-view {
//  min-height: calc(100vh - 68px);
//}

.sticky-goods-header li {
  position: relative;
  font-size: 28px;
  color: #5A6066;
  height: 68px;
  line-height: 68px;

  @media (min-width: @screen-medium + 1) {
    height: 45Px;
    line-height: 45Px;
    font-size: 16Px;
  }
}

.sticky-goods-header li:first-child {
  margin-right: 90px;
}

.sticky-goods-header li.active {
  color: #FF780A;
}

.sticky-goods-header li.active::after {
  content: '';
  display: block;
  width: 100%;
  height: 6px;
  background-image: linear-gradient(135deg, #FF780A 0%, #FF780A 100%);
  position: absolute;
  left: 0;
  bottom: 0;

  @media (min-width: @screen-medium + 1) {
    height: 4Px;
  }
}

.sticky-header {
  width: 100%;
  height: 88px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fff;
  box-shadow: 0 1px 0 0 #E5E5E5;
}

.sticky-header li {
  position: relative;
  font-size: 30px;
  color: #333333;
  height: 88px;
  line-height: 88px;
}

.sticky-header li:first-child {
  margin-right: 90px;
}

.sticky-header li.active::after {
  content: '';
  display: block;
  width: 100%;
  height: 6px;
  background-image: linear-gradient(135deg, #FFA033 0%, #FF6D33 100%);
  position: absolute;
  left: 0;
  bottom: 0;

  @media (min-width: @screen-medium + 1) {
    height: 4Px;
  }
}

.top-right-menus {
  position: absolute;
  top: 40Px;
  right: 15Px;
  z-index: 100;

  .circle {
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 58px;
    height: 58px;
    padding: 5px;
    background: #02020280;
    border-radius: 50%;

    @media (min-width: @screen-medium + 1) {
      width: 35Px;
      height: 35Px;
    }
  }

  .dot {
    width: 6px;
    height: 6px;
    background: #D8D8D8;
    border-radius: 50%;
  }
}

.goods-marketing {

  img {
    width: 100vw;
  }
}

.st-height-fix .scroll-list-wrap {
  height: calc(100vh - @fixed-bottom-height - 55Px);
}

.st-height-fix .fixed-bottom {
  bottom: 55Px;
}
</style>
