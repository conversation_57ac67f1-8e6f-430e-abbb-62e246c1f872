<template>
  <div class="wrapper" v-show="visible" @click="hidePop">
    <div class="share-dropdown" @click.stop="showPop">
      <slot>
        <div class="circle">
          <span class="dot" />
          <span class="dot" />
          <span class="dot" />
        </div>
      </slot>
    </div>
    <div class="dropdown" v-show="show">
      <ul class="menus" @click.stop>
        <li @click="toHomeHandler">
          <div class="item">
            <span class="icon home" />
            <span class="text">首页</span>
          </div>
        </li>
        <li @click="toCategoryHandler">
          <div class="item">
            <span class="icon category" />
            <span class="text">分类</span>
          </div>
        </li>
        <li @click="toUserHandler">
          <div class="item">
            <span class="icon user" />
            <span class="text">我的</span>
          </div>
        </li>
        <li @click="shareHandler" v-if="!isShowShare">
          <div class="item">
            <span class="icon share" />
            <span class="text">分享</span>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, getCurrentInstance, onActivated } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { isHarmonyOS } from 'commonkit'

defineProps({
  visible: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['share'])
const router = useRouter()
const route = useRoute()
const show = ref(false)

const trackText = computed(() => {
  const path = route.path
  let text = '点击右上角功能'
  if (/^\/goodslist/.test(path)) {
    text = '分类结果页点击右上角功能'
  } else if (/^\/goodsdetail/.test(path)) {
    text = '产品页点击右上角功能'
  }
  return text
})

const isShowShare = computed(() => {
  return isHarmonyOS
})

const hidePop = () => {
  show.value = false
}

const showPop = () => {
  show.value = true
}

// 点击右上角按钮-首页
const toHomeHandler = () => {
  router.push('/home')
  show.value = false
}

// 点击右上角按钮-分类
const toCategoryHandler = () => {
  router.push({ path: '/category', query: { _t: new Date().getTime().toString() } })
  show.value = false
}

// 点击右上角按钮-我的
const toUserHandler = () => {
  router.push('/user')
  show.value = false
}

// 点击右上角按钮-分享
const shareHandler = (e) => {
  emit('share', e)
  show.value = false
}

onActivated(() => {
  show.value = false
})
</script>

<style lang="less" scoped>
.wrapper {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.share-dropdown {
  cursor: pointer;
  padding: 8px;
}

.circle {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3px;
  padding: 8px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 20px;
  min-width: 32px;
  min-height: 32px;
  justify-content: center;
}

.dot {
  width: 4px;
  height: 4px;
  background: white;
  border-radius: 50%;
}

.dropdown {
  position: absolute;
  right: 50%;
  bottom: 0;

  &:before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
  }
}

.menus {
  @angle-right: 8px;
  @angle-size: 12px;
  @box-shadow: 0 1px 15px rgba(0, 0, 0, .4);
  @border-radius: 5px;

  position: absolute;
  top: (5px + @angle-size / 2);
  right: -(@angle-right + @angle-size / 2);
  background: #fff;
  color: #171E24;
  border-radius: @border-radius;
  box-shadow: @box-shadow;
  z-index: 2;

  &:after {
    content: '';
    position: absolute;
    top: -(@angle-size / 2 - 1px);
    right: @angle-right;
    width: @angle-size;
    height: @angle-size;
    background: #fff;
    transform: rotate(45deg) skew(5deg, 5deg);
    box-shadow: @box-shadow;
  }

  li {
    position: relative;
    background-color: #fff;
    z-index: 2;

    &:first-child {
      border-radius: @border-radius @border-radius 0 0;
    }

    &:last-child {
      border-radius: 0 0 @border-radius @border-radius;

      .item {
        border-bottom-color: #fff;
      }
    }
  }
}

.item {
  display: flex;
  align-items: center;
  width: 120px;
  margin: 0 8px;
  padding: 8px 0;
  border-bottom: 1px solid #E2E8EE;
}

.icon {
  display: inline-block;
  width: 23px;
  height: 23px;
  background-size: 23px;
  background-repeat: no-repeat;
  background-position: center center;

  &.home {
    background-image: url(../assets/icon-home.png);
  }

  &.user {
    background-image: url(../assets/icon-my.png);
  }

  &.share {
    background-image: url(../assets/icon-share.png);
  }

  &.category {
    background-image: url(../assets/icon-category.png);
  }
}

.text {
  padding: 0 11px;
  font-size: 16px;
  color: #171E24;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wrapper {
    top: 15px;
    right: 15px;
  }

  .share-dropdown {
    padding: 6px;
  }

  .circle {
    min-width: 28px;
    min-height: 28px;
    padding: 6px;
  }

  .dot {
    width: 3px;
    height: 3px;
  }
}
</style>