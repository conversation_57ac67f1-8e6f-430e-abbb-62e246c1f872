<template>
  <div class="wrapper">
    <p class="title">商品信息</p>
    <div ref="wrapper">
      <!-- 新京东的逻辑-->
      <div v-if="isGoodsIntroductionHtml" v-html="goodsIntroductionHtml"
           ref="content"
           class="wrapper-jd-inner"
           :style="{ transform: `scale(${scale})`, 'transform-origin': '0 0' }">
      </div>
      <div v-else v-lazy-container="{ selector: 'img'}">
        <img
          @load="imgLoaded"
          v-for="(item, index) in aImageList"
          :data-src="item"
          alt=""
          :key="index"
          data-name="goods-comp-goodsintroduce"
        />
      </div>
    </div>
    <img style="margin-top: 20px" src="../assets/businessLicense.png" alt="" />
  </div>
</template>

<script>
export default {
  props: [
    'currentSKU',
    'show'
  ],
  data () {
    return {
      clientWidth: document.documentElement.clientWidth,
      scale: 1,
      screenWidth: window.innerWidth,
      screenSize: ''
    }
  },
  computed: {
    isGoodsIntroductionHtml () {
      const { introduction } = this.currentSKU
      if (introduction) {
        return true
      }
      return false
    },
    aImageList () {
      let imgurl = ''
      if (this.currentSKU.introduceList) {
        if (typeof this.currentSKU.introduceList !== 'string') {
          imgurl = this.currentSKU.introduceList.toString()
        } else {
          imgurl = this.currentSKU.introduceList
        }
      } else {
        imgurl = ''
      }
      return imgurl.split(',').filter(item => !!item)
    },
    goodsIntroductionHtml () {
      return this.currentSKU.introduction
    }
  },
  watch: {
    currentSKU: {
      handler (val) {
        if (val) {
          // 重置京东样式
          this.currentSKU?.introduction && this.onResize()
        }
      },
      deep: true
    }
  },
  methods: {
    determineScreenSize () {
      let size
      if (this.screenWidth < 320) {
        size = 'type1'
      } else if (this.screenWidth >= 320 && this.screenWidth <= 540) {
        size = 'type2'
      } else {
        size = 'type3'
      }
      this.screenSize = size
      this.jdDetailLoaded()
    },
    onResize () {
      this.screenWidth = window.innerWidth
      this.determineScreenSize()
    },
    // 去除所有子元素的内联样式
    removeInlineStyles (parentElement) {
      // 遍历父元素的所有子元素
      for (const child of parentElement.childNodes) {
        // 检查节点是否为元素节点
        if (child.nodeType === 1) {
          if (child.tagName === 'IMG' || child.tagName === 'TABLE') {
            // 移除img元素的内联样式
            child.setAttribute('width', '100%')
            child.setAttribute('height', 'auto')
          } else {
            // 移除其他元素的width和height内联样式
            child.style.width = ''
          }
          this.removeInlineStyles(child)
        }
      }
    },
    jdDetailLoaded () {
      // this.onResize()
      setTimeout(() => {
        const $el = document.querySelector('.ssd-module-wrap') ||
          document.querySelector('.ssd-module-mobile-wrap') || document.querySelector('.wrapper-jd-inner') ||
          document.querySelector('.wrapper-jd-inner')?.firstElementChild
        if ($el) {
          this.removeInlineStyles($el)
          const contentComputedStyle = window.getComputedStyle($el)
          const realWidth = parseInt(contentComputedStyle.width)
          const realHeight = parseInt(contentComputedStyle.height)
          this.clientWidth = document.documentElement.clientWidth
          if (this.screenSize === 'type3') {
            const scaleHeight = (this.clientWidth * 0.7) / realWidth * realHeight
            this.scale = (this.clientWidth - 160) / realWidth
            this.$refs.wrapper.style.cssText = `height: ${scaleHeight}px`
            return
          }
          const scaleHeight = this.clientWidth / realWidth * realHeight
          this.scale = this.clientWidth / realWidth
          this.$refs.wrapper.style.cssText = `height: ${scaleHeight}px`
        }
      }, 100)
    },
    imgLoaded () {
    }
  },
  created () {
    this.determineScreenSize()
  },
  destroyed () {
  }
}
</script>

<style scoped lang="less">
@screen-small: 320px;
@screen-medium: 540px;

.wrapper {
  width: 100%;
  font-size: 16px;

  @media (min-width: @screen-medium + 1) {
    padding: 0 18%;
  }

  .wrapper-inner {
    br {
      display: none;
    }
  }

  img {
    display: block;
    width: 100%;
  }

  /deep/ .ssd-module-wrap,
  /deep/ .ssd-module-mobile-wrap {
    font-size: 16px;
  }
}

.title {
  height: 43px;
  line-height: 43px;
  font-size: 15px;
  color: #171e24;
  text-align: center;
}
</style>
